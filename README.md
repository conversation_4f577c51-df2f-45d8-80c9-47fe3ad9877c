# Netty SMTP Server

一个基于Java、SpringBoot和Netty框架实现的SMTP邮件发送服务器，用于学习和理解邮件发送的底层原理。

## 项目特性

- 🚀 **基于Netty的高性能TCP服务器**：使用Netty框架实现底层网络通信
- 📧 **完整的SMTP协议支持**：实现HELO/EHLO、MAIL FROM、RCPT TO、DATA等核心命令
- 🔄 **邮件转发功能**：支持向各大邮件运营商（Gmail、Outlook、Yahoo等）转发邮件
- 🏠 **本地邮件投递**：支持本地局域网邮件投递和存储
- 📊 **全面的日志系统**：详细记录连接、协议交互、邮件投递等信息
- 🧪 **完整的测试套件**：包含单元测试、集成测试和功能测试
- 🔧 **灵活的配置管理**：基于SpringBoot的配置系统
- 🌐 **REST API管理**：提供HTTP接口管理服务器和查看邮件

## 技术栈

- **Java 17**
- **Spring Boot 3.2.0**
- **Netty 4.1.104**
- **Maven**
- **JUnit 5**
- **Logback**

## 快速开始

### 1. 环境要求

- JDK 17+
- Maven 3.6+

### 2. 克隆项目

```bash
git clone <repository-url>
cd netty-smtp
```

### 3. 编译项目

```bash
mvn clean compile
```

### 4. 运行测试

```bash
mvn test
```

### 5. 启动服务器

```bash
mvn spring-boot:run
```

服务器将在以下端口启动：
- SMTP服务器：`localhost:2525`
- HTTP管理接口：`localhost:8080`

## 配置说明

主要配置文件：`src/main/resources/application.yml`

### SMTP服务器配置

```yaml
smtp:
  server:
    port: 2525                    # SMTP服务端口
    hostname: localhost           # 服务器主机名
    max-connections: 100          # 最大连接数
    connection-timeout: 300       # 连接超时时间(秒)
    ssl-enabled: false           # 是否启用SSL
    starttls-enabled: true       # 是否启用STARTTLS
```

### 邮件处理配置

```yaml
smtp:
  mail:
    max-size: 25                 # 最大邮件大小(MB)
    max-recipients: 100          # 最大收件人数量
    queue-size: 1000            # 邮件队列大小
    worker-threads: 10          # 处理线程数
```

### 转发配置

```yaml
smtp:
  relay:
    enabled: true               # 是否启用转发
    default-host: smtp.gmail.com
    default-port: 587
    auth:
      username: ${SMTP_USERNAME:}
      password: ${SMTP_PASSWORD:}
```

## 使用示例

### 1. 使用telnet测试

```bash
telnet localhost 2525
```

```
220 localhost ESMTP Service ready
HELO client.example.com
250 localhost Hello client.example.com
MAIL FROM:<<EMAIL>>
250 OK, <NAME_EMAIL>
RCPT TO:<recipient@localhost>
250 OK, recipient recipient@localhost
DATA
354 Start mail input; end with <CRLF>.<CRLF>
Subject: Test Message

This is a test message.
.
250 OK: Message accepted for delivery, ID=xxx
QUIT
221 Service closing transmission channel
```

### 2. 使用Java客户端

```java
try (SmtpTestClient client = new SmtpTestClient("localhost", 2525)) {
    client.connect();
    client.sendMail(
        "<EMAIL>",
        List.of("recipient@localhost"),
        "Test Subject",
        "Test message body"
    );
}
```

### 3. 使用REST API

查看服务器状态：
```bash
curl http://localhost:8080/api/smtp/status
```

查看邮箱邮件：
```bash
curl http://localhost:8080/api/smtp/mailboxes/recipient@localhost/messages
```

## 项目结构

```
src/
├── main/java/com/netty/smtp/
│   ├── config/          # 配置类
│   ├── controller/      # REST控制器
│   ├── handler/         # Netty处理器
│   ├── model/           # 数据模型
│   ├── protocol/        # SMTP协议实现
│   ├── server/          # 服务器核心
│   ├── service/         # 业务服务
│   └── util/            # 工具类
├── test/java/com/netty/smtp/
│   ├── integration/     # 集成测试
│   ├── protocol/        # 协议测试
│   ├── service/         # 服务测试
│   └── util/            # 测试工具
└── main/resources/
    ├── application.yml  # 主配置文件
    └── logback-spring.xml # 日志配置
```

## 核心功能

### 1. SMTP协议支持

- **HELO/EHLO**：客户端标识
- **MAIL FROM**：指定发件人
- **RCPT TO**：指定收件人
- **DATA**：传输邮件内容
- **RSET**：重置会话
- **QUIT**：结束会话
- **NOOP**：空操作
- **HELP**：帮助信息

### 2. 邮件路由

- **本地投递**：投递到本地邮箱文件
- **远程转发**：转发到外部SMTP服务器
- **DNS解析**：自动解析MX记录
- **智能路由**：根据域名选择最佳投递方式

### 3. 日志系统

- **连接日志**：记录客户端连接信息
- **协议日志**：记录SMTP命令交互
- **投递日志**：记录邮件投递过程
- **错误日志**：记录系统错误信息

## 测试

### 运行所有测试

```bash
mvn test
```

### 运行特定测试

```bash
# 协议测试
mvn test -Dtest=SmtpCommandProcessorTest

# 集成测试
mvn test -Dtest=SmtpServerIntegrationTest

# 服务测试
mvn test -Dtest=LocalDeliveryServiceTest
```

## 开发指南

### 添加新的SMTP命令

1. 在`SmtpCommand`枚举中添加新命令
2. 在`SmtpCommandProcessor`中实现处理逻辑
3. 添加相应的测试用例

### 扩展邮件投递功能

1. 实现新的投递服务接口
2. 在`EmailDeliveryService`中集成
3. 添加配置选项和测试

## 故障排除

### 常见问题

1. **端口被占用**：修改`application.yml`中的端口配置
2. **权限问题**：确保有写入`mailboxes`目录的权限
3. **DNS解析失败**：检查网络连接和DNS配置

### 日志查看

日志文件位置：`logs/`目录下
- `smtp-server.log`：主日志文件
- `smtp-protocol.log`：协议交互日志
- `mail-delivery.log`：邮件投递日志
- `error.log`：错误日志

## 贡献

欢迎提交Issue和Pull Request来改进项目。

## 许可证

MIT License
