<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report-3.0.xsd" version="3.0" name="com.netty.smtp.protocol.SmtpDataProcessorTest" time="0.008" tests="10" errors="0" skipped="0" failures="0">
  <properties>
    <property name="gopherProxySet" value="false"/>
    <property name="awt.toolkit" value="sun.lwawt.macosx.LWCToolkit"/>
    <property name="file.encoding.pkg" value="sun.io"/>
    <property name="java.specification.version" value="1.8"/>
    <property name="sun.cpu.isalist" value=""/>
    <property name="sun.jnu.encoding" value="UTF-8"/>
    <property name="java.class.path" value="/Users/<USER>/Documents/business-work/netty-smtp/target/test-classes:/Users/<USER>/Documents/business-work/netty-smtp/target/classes:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/springframework/boot/spring-boot-starter/2.7.18/spring-boot-starter-2.7.18.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/springframework/boot/spring-boot/2.7.18/spring-boot-2.7.18.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/springframework/spring-context/5.3.31/spring-context-5.3.31.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/springframework/boot/spring-boot-autoconfigure/2.7.18/spring-boot-autoconfigure-2.7.18.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/springframework/boot/spring-boot-starter-logging/2.7.18/spring-boot-starter-logging-2.7.18.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/apache/logging/log4j/log4j-to-slf4j/2.17.2/log4j-to-slf4j-2.17.2.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/apache/logging/log4j/log4j-api/2.17.2/log4j-api-2.17.2.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/slf4j/jul-to-slf4j/1.7.36/jul-to-slf4j-1.7.36.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/jakarta/annotation/jakarta.annotation-api/1.3.5/jakarta.annotation-api-1.3.5.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/springframework/spring-core/5.3.31/spring-core-5.3.31.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/springframework/spring-jcl/5.3.31/spring-jcl-5.3.31.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/yaml/snakeyaml/1.30/snakeyaml-1.30.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/springframework/boot/spring-boot-starter-web/2.7.18/spring-boot-starter-web-2.7.18.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/springframework/boot/spring-boot-starter-json/2.7.18/spring-boot-starter-json-2.7.18.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/com/fasterxml/jackson/core/jackson-databind/2.13.5/jackson-databind-2.13.5.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/com/fasterxml/jackson/core/jackson-annotations/2.13.5/jackson-annotations-2.13.5.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/com/fasterxml/jackson/core/jackson-core/2.13.5/jackson-core-2.13.5.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.13.5/jackson-datatype-jdk8-2.13.5.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.13.5/jackson-datatype-jsr310-2.13.5.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/com/fasterxml/jackson/module/jackson-module-parameter-names/2.13.5/jackson-module-parameter-names-2.13.5.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/springframework/boot/spring-boot-starter-tomcat/2.7.18/spring-boot-starter-tomcat-2.7.18.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/apache/tomcat/embed/tomcat-embed-core/9.0.83/tomcat-embed-core-9.0.83.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/apache/tomcat/embed/tomcat-embed-websocket/9.0.83/tomcat-embed-websocket-9.0.83.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/springframework/spring-web/5.3.31/spring-web-5.3.31.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/springframework/spring-beans/5.3.31/spring-beans-5.3.31.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/springframework/spring-webmvc/5.3.31/spring-webmvc-5.3.31.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/springframework/spring-aop/5.3.31/spring-aop-5.3.31.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/springframework/spring-expression/5.3.31/spring-expression-5.3.31.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/springframework/boot/spring-boot-starter-validation/2.7.18/spring-boot-starter-validation-2.7.18.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/apache/tomcat/embed/tomcat-embed-el/9.0.83/tomcat-embed-el-9.0.83.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/hibernate/validator/hibernate-validator/6.2.5.Final/hibernate-validator-6.2.5.Final.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/jakarta/validation/jakarta.validation-api/2.0.2/jakarta.validation-api-2.0.2.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/jboss/logging/jboss-logging/3.4.3.Final/jboss-logging-3.4.3.Final.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/com/fasterxml/classmate/1.5.1/classmate-1.5.1.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/springframework/boot/spring-boot-configuration-processor/2.7.18/spring-boot-configuration-processor-2.7.18.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/io/netty/netty-all/4.1.104.Final/netty-all-4.1.104.Final.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/io/netty/netty-buffer/4.1.104.Final/netty-buffer-4.1.104.Final.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/io/netty/netty-codec/4.1.104.Final/netty-codec-4.1.104.Final.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/io/netty/netty-codec-dns/4.1.104.Final/netty-codec-dns-4.1.104.Final.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/io/netty/netty-codec-haproxy/4.1.104.Final/netty-codec-haproxy-4.1.104.Final.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/io/netty/netty-codec-http/4.1.104.Final/netty-codec-http-4.1.104.Final.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/io/netty/netty-codec-http2/4.1.104.Final/netty-codec-http2-4.1.104.Final.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/io/netty/netty-codec-memcache/4.1.104.Final/netty-codec-memcache-4.1.104.Final.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/io/netty/netty-codec-mqtt/4.1.104.Final/netty-codec-mqtt-4.1.104.Final.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/io/netty/netty-codec-redis/4.1.104.Final/netty-codec-redis-4.1.104.Final.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/io/netty/netty-codec-smtp/4.1.104.Final/netty-codec-smtp-4.1.104.Final.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/io/netty/netty-codec-socks/4.1.104.Final/netty-codec-socks-4.1.104.Final.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/io/netty/netty-codec-stomp/4.1.104.Final/netty-codec-stomp-4.1.104.Final.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/io/netty/netty-codec-xml/4.1.104.Final/netty-codec-xml-4.1.104.Final.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/io/netty/netty-common/4.1.104.Final/netty-common-4.1.104.Final.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/io/netty/netty-handler/4.1.104.Final/netty-handler-4.1.104.Final.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/io/netty/netty-transport-native-unix-common/4.1.104.Final/netty-transport-native-unix-common-4.1.104.Final.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/io/netty/netty-handler-proxy/4.1.104.Final/netty-handler-proxy-4.1.104.Final.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/io/netty/netty-handler-ssl-ocsp/4.1.104.Final/netty-handler-ssl-ocsp-4.1.104.Final.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/io/netty/netty-resolver/4.1.104.Final/netty-resolver-4.1.104.Final.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/io/netty/netty-resolver-dns/4.1.104.Final/netty-resolver-dns-4.1.104.Final.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/io/netty/netty-transport/4.1.104.Final/netty-transport-4.1.104.Final.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/io/netty/netty-transport-rxtx/4.1.104.Final/netty-transport-rxtx-4.1.104.Final.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/io/netty/netty-transport-sctp/4.1.104.Final/netty-transport-sctp-4.1.104.Final.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/io/netty/netty-transport-udt/4.1.104.Final/netty-transport-udt-4.1.104.Final.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/io/netty/netty-transport-classes-epoll/4.1.104.Final/netty-transport-classes-epoll-4.1.104.Final.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/io/netty/netty-transport-classes-kqueue/4.1.104.Final/netty-transport-classes-kqueue-4.1.104.Final.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/io/netty/netty-resolver-dns-classes-macos/4.1.104.Final/netty-resolver-dns-classes-macos-4.1.104.Final.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/io/netty/netty-transport-native-epoll/4.1.104.Final/netty-transport-native-epoll-4.1.104.Final-linux-x86_64.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/io/netty/netty-transport-native-epoll/4.1.104.Final/netty-transport-native-epoll-4.1.104.Final-linux-aarch_64.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/io/netty/netty-transport-native-epoll/4.1.104.Final/netty-transport-native-epoll-4.1.104.Final-linux-riscv64.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/io/netty/netty-transport-native-kqueue/4.1.104.Final/netty-transport-native-kqueue-4.1.104.Final-osx-x86_64.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/io/netty/netty-transport-native-kqueue/4.1.104.Final/netty-transport-native-kqueue-4.1.104.Final-osx-aarch_64.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/io/netty/netty-resolver-dns-native-macos/4.1.104.Final/netty-resolver-dns-native-macos-4.1.104.Final-osx-x86_64.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/io/netty/netty-resolver-dns-native-macos/4.1.104.Final/netty-resolver-dns-native-macos-4.1.104.Final-osx-aarch_64.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/jakarta/mail/jakarta.mail-api/2.1.2/jakarta.mail-api-2.1.2.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/jakarta/activation/jakarta.activation-api/1.2.2/jakarta.activation-api-1.2.2.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/eclipse/angus/angus-mail/2.0.2/angus-mail-2.0.2.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/eclipse/angus/angus-activation/2.0.1/angus-activation-2.0.1.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/dnsjava/dnsjava/3.5.2/dnsjava-3.5.2.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/slf4j/slf4j-api/1.7.36/slf4j-api-1.7.36.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/apache/commons/commons-lang3/3.13.0/commons-lang3-3.13.0.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/commons-validator/commons-validator/1.7/commons-validator-1.7.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/commons-beanutils/commons-beanutils/1.9.4/commons-beanutils-1.9.4.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/commons-digester/commons-digester/2.1/commons-digester-2.1.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/commons-logging/commons-logging/1.2/commons-logging-1.2.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/commons-collections/commons-collections/3.2.2/commons-collections-3.2.2.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/ch/qos/logback/logback-classic/1.4.14/logback-classic-1.4.14.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/ch/qos/logback/logback-core/1.4.14/logback-core-1.4.14.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/springframework/boot/spring-boot-starter-test/2.7.18/spring-boot-starter-test-2.7.18.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/springframework/boot/spring-boot-test/2.7.18/spring-boot-test-2.7.18.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/springframework/boot/spring-boot-test-autoconfigure/2.7.18/spring-boot-test-autoconfigure-2.7.18.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/com/jayway/jsonpath/json-path/2.7.0/json-path-2.7.0.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/net/minidev/json-smart/2.4.11/json-smart-2.4.11.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/net/minidev/accessors-smart/2.4.11/accessors-smart-2.4.11.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/ow2/asm/asm/9.3/asm-9.3.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/jakarta/xml/bind/jakarta.xml.bind-api/2.3.3/jakarta.xml.bind-api-2.3.3.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/assertj/assertj-core/3.22.0/assertj-core-3.22.0.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/hamcrest/hamcrest/2.2/hamcrest-2.2.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/mockito/mockito-junit-jupiter/4.5.1/mockito-junit-jupiter-4.5.1.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/skyscreamer/jsonassert/1.5.1/jsonassert-1.5.1.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/springframework/spring-test/5.3.31/spring-test-5.3.31.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/xmlunit/xmlunit-core/2.9.1/xmlunit-core-2.9.1.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/junit/jupiter/junit-jupiter/5.8.2/junit-jupiter-5.8.2.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/junit/jupiter/junit-jupiter-api/5.8.2/junit-jupiter-api-5.8.2.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/opentest4j/opentest4j/1.2.0/opentest4j-1.2.0.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/junit/platform/junit-platform-commons/1.8.2/junit-platform-commons-1.8.2.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/apiguardian/apiguardian-api/1.1.2/apiguardian-api-1.1.2.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/junit/jupiter/junit-jupiter-params/5.8.2/junit-jupiter-params-5.8.2.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/junit/jupiter/junit-jupiter-engine/5.8.2/junit-jupiter-engine-5.8.2.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/junit/platform/junit-platform-engine/1.8.2/junit-platform-engine-1.8.2.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/mockito/mockito-core/4.5.1/mockito-core-4.5.1.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/net/bytebuddy/byte-buddy/1.12.23/byte-buddy-1.12.23.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/net/bytebuddy/byte-buddy-agent/1.12.23/byte-buddy-agent-1.12.23.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/objenesis/objenesis/3.2/objenesis-3.2.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/testcontainers/junit-jupiter/1.17.6/junit-jupiter-1.17.6.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/testcontainers/testcontainers/1.17.6/testcontainers-1.17.6.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/hamcrest/hamcrest-core/2.2/hamcrest-core-2.2.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/apache/commons/commons-compress/1.22/commons-compress-1.22.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/rnorth/duct-tape/duct-tape/1.0.8/duct-tape-1.0.8.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/jetbrains/annotations/17.0.0/annotations-17.0.0.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/com/github/docker-java/docker-java-api/3.2.13/docker-java-api-3.2.13.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/com/github/docker-java/docker-java-transport-zerodep/3.2.13/docker-java-transport-zerodep-3.2.13.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/com/github/docker-java/docker-java-transport/3.2.13/docker-java-transport-3.2.13.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/net/java/dev/jna/jna/5.8.0/jna-5.8.0.jar:"/>
    <property name="java.vm.vendor" value="Amazon.com Inc."/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="java.vendor.url" value="https://aws.amazon.com/corretto/"/>
    <property name="catalina.useNaming" value="false"/>
    <property name="user.timezone" value="Asia/Jakarta"/>
    <property name="java.vm.specification.version" value="1.8"/>
    <property name="os.name" value="Mac OS X"/>
    <property name="user.country" value="CN"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="sun.boot.library.path" value="/Users/<USER>/.sdkman/candidates/java/8.0.442-amzn/jre/lib"/>
    <property name="sun.java.command" value="/Users/<USER>/Documents/business-work/netty-smtp/target/surefire/surefirebooter-20250731224005959_3.jar /Users/<USER>/Documents/business-work/netty-smtp/target/surefire 2025-07-31T22-40-04_831-jvmRun1 surefire-20250731224005959_1tmp surefire_0-20250731224005959_2tmp"/>
    <property name="http.nonProxyHosts" value="127.0.0.1|***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|localhost|*.localhost|local|*.local|crashlytics.com|*.crashlytics.com|&lt;local&gt;|*.&lt;local&gt;"/>
    <property name="surefire.test.class.path" value="/Users/<USER>/Documents/business-work/netty-smtp/target/test-classes:/Users/<USER>/Documents/business-work/netty-smtp/target/classes:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/springframework/boot/spring-boot-starter/2.7.18/spring-boot-starter-2.7.18.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/springframework/boot/spring-boot/2.7.18/spring-boot-2.7.18.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/springframework/spring-context/5.3.31/spring-context-5.3.31.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/springframework/boot/spring-boot-autoconfigure/2.7.18/spring-boot-autoconfigure-2.7.18.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/springframework/boot/spring-boot-starter-logging/2.7.18/spring-boot-starter-logging-2.7.18.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/apache/logging/log4j/log4j-to-slf4j/2.17.2/log4j-to-slf4j-2.17.2.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/apache/logging/log4j/log4j-api/2.17.2/log4j-api-2.17.2.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/slf4j/jul-to-slf4j/1.7.36/jul-to-slf4j-1.7.36.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/jakarta/annotation/jakarta.annotation-api/1.3.5/jakarta.annotation-api-1.3.5.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/springframework/spring-core/5.3.31/spring-core-5.3.31.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/springframework/spring-jcl/5.3.31/spring-jcl-5.3.31.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/yaml/snakeyaml/1.30/snakeyaml-1.30.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/springframework/boot/spring-boot-starter-web/2.7.18/spring-boot-starter-web-2.7.18.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/springframework/boot/spring-boot-starter-json/2.7.18/spring-boot-starter-json-2.7.18.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/com/fasterxml/jackson/core/jackson-databind/2.13.5/jackson-databind-2.13.5.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/com/fasterxml/jackson/core/jackson-annotations/2.13.5/jackson-annotations-2.13.5.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/com/fasterxml/jackson/core/jackson-core/2.13.5/jackson-core-2.13.5.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.13.5/jackson-datatype-jdk8-2.13.5.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.13.5/jackson-datatype-jsr310-2.13.5.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/com/fasterxml/jackson/module/jackson-module-parameter-names/2.13.5/jackson-module-parameter-names-2.13.5.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/springframework/boot/spring-boot-starter-tomcat/2.7.18/spring-boot-starter-tomcat-2.7.18.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/apache/tomcat/embed/tomcat-embed-core/9.0.83/tomcat-embed-core-9.0.83.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/apache/tomcat/embed/tomcat-embed-websocket/9.0.83/tomcat-embed-websocket-9.0.83.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/springframework/spring-web/5.3.31/spring-web-5.3.31.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/springframework/spring-beans/5.3.31/spring-beans-5.3.31.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/springframework/spring-webmvc/5.3.31/spring-webmvc-5.3.31.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/springframework/spring-aop/5.3.31/spring-aop-5.3.31.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/springframework/spring-expression/5.3.31/spring-expression-5.3.31.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/springframework/boot/spring-boot-starter-validation/2.7.18/spring-boot-starter-validation-2.7.18.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/apache/tomcat/embed/tomcat-embed-el/9.0.83/tomcat-embed-el-9.0.83.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/hibernate/validator/hibernate-validator/6.2.5.Final/hibernate-validator-6.2.5.Final.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/jakarta/validation/jakarta.validation-api/2.0.2/jakarta.validation-api-2.0.2.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/jboss/logging/jboss-logging/3.4.3.Final/jboss-logging-3.4.3.Final.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/com/fasterxml/classmate/1.5.1/classmate-1.5.1.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/springframework/boot/spring-boot-configuration-processor/2.7.18/spring-boot-configuration-processor-2.7.18.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/io/netty/netty-all/4.1.104.Final/netty-all-4.1.104.Final.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/io/netty/netty-buffer/4.1.104.Final/netty-buffer-4.1.104.Final.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/io/netty/netty-codec/4.1.104.Final/netty-codec-4.1.104.Final.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/io/netty/netty-codec-dns/4.1.104.Final/netty-codec-dns-4.1.104.Final.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/io/netty/netty-codec-haproxy/4.1.104.Final/netty-codec-haproxy-4.1.104.Final.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/io/netty/netty-codec-http/4.1.104.Final/netty-codec-http-4.1.104.Final.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/io/netty/netty-codec-http2/4.1.104.Final/netty-codec-http2-4.1.104.Final.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/io/netty/netty-codec-memcache/4.1.104.Final/netty-codec-memcache-4.1.104.Final.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/io/netty/netty-codec-mqtt/4.1.104.Final/netty-codec-mqtt-4.1.104.Final.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/io/netty/netty-codec-redis/4.1.104.Final/netty-codec-redis-4.1.104.Final.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/io/netty/netty-codec-smtp/4.1.104.Final/netty-codec-smtp-4.1.104.Final.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/io/netty/netty-codec-socks/4.1.104.Final/netty-codec-socks-4.1.104.Final.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/io/netty/netty-codec-stomp/4.1.104.Final/netty-codec-stomp-4.1.104.Final.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/io/netty/netty-codec-xml/4.1.104.Final/netty-codec-xml-4.1.104.Final.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/io/netty/netty-common/4.1.104.Final/netty-common-4.1.104.Final.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/io/netty/netty-handler/4.1.104.Final/netty-handler-4.1.104.Final.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/io/netty/netty-transport-native-unix-common/4.1.104.Final/netty-transport-native-unix-common-4.1.104.Final.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/io/netty/netty-handler-proxy/4.1.104.Final/netty-handler-proxy-4.1.104.Final.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/io/netty/netty-handler-ssl-ocsp/4.1.104.Final/netty-handler-ssl-ocsp-4.1.104.Final.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/io/netty/netty-resolver/4.1.104.Final/netty-resolver-4.1.104.Final.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/io/netty/netty-resolver-dns/4.1.104.Final/netty-resolver-dns-4.1.104.Final.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/io/netty/netty-transport/4.1.104.Final/netty-transport-4.1.104.Final.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/io/netty/netty-transport-rxtx/4.1.104.Final/netty-transport-rxtx-4.1.104.Final.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/io/netty/netty-transport-sctp/4.1.104.Final/netty-transport-sctp-4.1.104.Final.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/io/netty/netty-transport-udt/4.1.104.Final/netty-transport-udt-4.1.104.Final.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/io/netty/netty-transport-classes-epoll/4.1.104.Final/netty-transport-classes-epoll-4.1.104.Final.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/io/netty/netty-transport-classes-kqueue/4.1.104.Final/netty-transport-classes-kqueue-4.1.104.Final.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/io/netty/netty-resolver-dns-classes-macos/4.1.104.Final/netty-resolver-dns-classes-macos-4.1.104.Final.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/io/netty/netty-transport-native-epoll/4.1.104.Final/netty-transport-native-epoll-4.1.104.Final-linux-x86_64.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/io/netty/netty-transport-native-epoll/4.1.104.Final/netty-transport-native-epoll-4.1.104.Final-linux-aarch_64.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/io/netty/netty-transport-native-epoll/4.1.104.Final/netty-transport-native-epoll-4.1.104.Final-linux-riscv64.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/io/netty/netty-transport-native-kqueue/4.1.104.Final/netty-transport-native-kqueue-4.1.104.Final-osx-x86_64.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/io/netty/netty-transport-native-kqueue/4.1.104.Final/netty-transport-native-kqueue-4.1.104.Final-osx-aarch_64.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/io/netty/netty-resolver-dns-native-macos/4.1.104.Final/netty-resolver-dns-native-macos-4.1.104.Final-osx-x86_64.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/io/netty/netty-resolver-dns-native-macos/4.1.104.Final/netty-resolver-dns-native-macos-4.1.104.Final-osx-aarch_64.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/jakarta/mail/jakarta.mail-api/2.1.2/jakarta.mail-api-2.1.2.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/jakarta/activation/jakarta.activation-api/1.2.2/jakarta.activation-api-1.2.2.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/eclipse/angus/angus-mail/2.0.2/angus-mail-2.0.2.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/eclipse/angus/angus-activation/2.0.1/angus-activation-2.0.1.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/dnsjava/dnsjava/3.5.2/dnsjava-3.5.2.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/slf4j/slf4j-api/1.7.36/slf4j-api-1.7.36.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/apache/commons/commons-lang3/3.13.0/commons-lang3-3.13.0.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/commons-validator/commons-validator/1.7/commons-validator-1.7.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/commons-beanutils/commons-beanutils/1.9.4/commons-beanutils-1.9.4.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/commons-digester/commons-digester/2.1/commons-digester-2.1.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/commons-logging/commons-logging/1.2/commons-logging-1.2.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/commons-collections/commons-collections/3.2.2/commons-collections-3.2.2.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/ch/qos/logback/logback-classic/1.4.14/logback-classic-1.4.14.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/ch/qos/logback/logback-core/1.4.14/logback-core-1.4.14.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/springframework/boot/spring-boot-starter-test/2.7.18/spring-boot-starter-test-2.7.18.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/springframework/boot/spring-boot-test/2.7.18/spring-boot-test-2.7.18.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/springframework/boot/spring-boot-test-autoconfigure/2.7.18/spring-boot-test-autoconfigure-2.7.18.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/com/jayway/jsonpath/json-path/2.7.0/json-path-2.7.0.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/net/minidev/json-smart/2.4.11/json-smart-2.4.11.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/net/minidev/accessors-smart/2.4.11/accessors-smart-2.4.11.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/ow2/asm/asm/9.3/asm-9.3.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/jakarta/xml/bind/jakarta.xml.bind-api/2.3.3/jakarta.xml.bind-api-2.3.3.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/assertj/assertj-core/3.22.0/assertj-core-3.22.0.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/hamcrest/hamcrest/2.2/hamcrest-2.2.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/mockito/mockito-junit-jupiter/4.5.1/mockito-junit-jupiter-4.5.1.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/skyscreamer/jsonassert/1.5.1/jsonassert-1.5.1.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/springframework/spring-test/5.3.31/spring-test-5.3.31.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/xmlunit/xmlunit-core/2.9.1/xmlunit-core-2.9.1.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/junit/jupiter/junit-jupiter/5.8.2/junit-jupiter-5.8.2.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/junit/jupiter/junit-jupiter-api/5.8.2/junit-jupiter-api-5.8.2.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/opentest4j/opentest4j/1.2.0/opentest4j-1.2.0.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/junit/platform/junit-platform-commons/1.8.2/junit-platform-commons-1.8.2.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/apiguardian/apiguardian-api/1.1.2/apiguardian-api-1.1.2.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/junit/jupiter/junit-jupiter-params/5.8.2/junit-jupiter-params-5.8.2.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/junit/jupiter/junit-jupiter-engine/5.8.2/junit-jupiter-engine-5.8.2.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/junit/platform/junit-platform-engine/1.8.2/junit-platform-engine-1.8.2.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/mockito/mockito-core/4.5.1/mockito-core-4.5.1.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/net/bytebuddy/byte-buddy/1.12.23/byte-buddy-1.12.23.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/net/bytebuddy/byte-buddy-agent/1.12.23/byte-buddy-agent-1.12.23.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/objenesis/objenesis/3.2/objenesis-3.2.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/testcontainers/junit-jupiter/1.17.6/junit-jupiter-1.17.6.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/testcontainers/testcontainers/1.17.6/testcontainers-1.17.6.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/hamcrest/hamcrest-core/2.2/hamcrest-core-2.2.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/apache/commons/commons-compress/1.22/commons-compress-1.22.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/rnorth/duct-tape/duct-tape/1.0.8/duct-tape-1.0.8.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/org/jetbrains/annotations/17.0.0/annotations-17.0.0.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/com/github/docker-java/docker-java-api/3.2.13/docker-java-api-3.2.13.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/com/github/docker-java/docker-java-transport-zerodep/3.2.13/docker-java-transport-zerodep-3.2.13.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/com/github/docker-java/docker-java-transport/3.2.13/docker-java-transport-3.2.13.jar:/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2/net/java/dev/jna/jna/5.8.0/jna-5.8.0.jar:"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="/Users/<USER>"/>
    <property name="user.language" value="zh"/>
    <property name="java.specification.vendor" value="Oracle Corporation"/>
    <property name="java.home" value="/Users/<USER>/.sdkman/candidates/java/8.0.442-amzn/jre"/>
    <property name="file.separator" value="/"/>
    <property name="basedir" value="/Users/<USER>/Documents/business-work/netty-smtp"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="FILE_LOG_CHARSET" value="UTF-8"/>
    <property name="java.awt.graphicsenv" value="sun.awt.CGraphicsEnvironment"/>
    <property name="java.awt.headless" value="true"/>
    <property name="surefire.real.class.path" value="/Users/<USER>/Documents/business-work/netty-smtp/target/surefire/surefirebooter-20250731224005959_3.jar"/>
    <property name="sun.boot.class.path" value="/Users/<USER>/.sdkman/candidates/java/8.0.442-amzn/jre/lib/resources.jar:/Users/<USER>/.sdkman/candidates/java/8.0.442-amzn/jre/lib/rt.jar:/Users/<USER>/.sdkman/candidates/java/8.0.442-amzn/jre/lib/sunrsasign.jar:/Users/<USER>/.sdkman/candidates/java/8.0.442-amzn/jre/lib/jsse.jar:/Users/<USER>/.sdkman/candidates/java/8.0.442-amzn/jre/lib/jce.jar:/Users/<USER>/.sdkman/candidates/java/8.0.442-amzn/jre/lib/charsets.jar:/Users/<USER>/.sdkman/candidates/java/8.0.442-amzn/jre/lib/jfr.jar:/Users/<USER>/.sdkman/candidates/java/8.0.442-amzn/jre/classes"/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="ftp.nonProxyHosts" value="127.0.0.1|***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|localhost|*.localhost|local|*.local|crashlytics.com|*.crashlytics.com|&lt;local&gt;|*.&lt;local&gt;"/>
    <property name="java.runtime.version" value="1.8.0_442-b06"/>
    <property name="user.name" value="wlf"/>
    <property name="path.separator" value=":"/>
    <property name="os.version" value="15.5"/>
    <property name="java.endorsed.dirs" value="/Users/<USER>/.sdkman/candidates/java/8.0.442-amzn/jre/lib/endorsed"/>
    <property name="java.runtime.name" value="OpenJDK Runtime Environment"/>
    <property name="FILE_LOG_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%logger{36}] - %msg%n"/>
    <property name="file.encoding" value="UTF-8"/>
    <property name="spring.beaninfo.ignore" value="true"/>
    <property name="java.vm.name" value="OpenJDK 64-Bit Server VM"/>
    <property name="LOG_FILE" value="logs/smtp-server.log"/>
    <property name="localRepository" value="/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2"/>
    <property name="java.vendor.url.bug" value="https://github.com/corretto/corretto-8/issues/"/>
    <property name="java.io.tmpdir" value="/var/folders/kk/hjwj6mg16n93mjs0d4qv4wvm0000gn/T/"/>
    <property name="idea.version" value="2025.1.3"/>
    <property name="catalina.home" value="/private/var/folders/kk/hjwj6mg16n93mjs0d4qv4wvm0000gn/T/tomcat.0.5473145891718122850"/>
    <property name="java.version" value="1.8.0_442"/>
    <property name="user.dir" value="/Users/<USER>/Documents/business-work/netty-smtp"/>
    <property name="os.arch" value="aarch64"/>
    <property name="PID" value="23757"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="java.awt.printerjob" value="sun.lwawt.macosx.CPrinterJob"/>
    <property name="sun.os.patch.level" value="unknown"/>
    <property name="CONSOLE_LOG_CHARSET" value="UTF-8"/>
    <property name="catalina.base" value="/private/var/folders/kk/hjwj6mg16n93mjs0d4qv4wvm0000gn/T/tomcat.0.5473145891718122850"/>
    <property name="java.library.path" value="/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:."/>
    <property name="java.vendor" value="Amazon.com Inc."/>
    <property name="java.vm.info" value="mixed mode"/>
    <property name="java.vm.version" value="25.442-b06"/>
    <property name="java.specification.maintenance.version" value="6"/>
    <property name="sun.io.unicode.encoding" value="UnicodeBig"/>
    <property name="java.ext.dirs" value="/Users/<USER>/Library/Java/Extensions:/Users/<USER>/.sdkman/candidates/java/8.0.442-amzn/jre/lib/ext:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java"/>
    <property name="maven.repo.local" value="/Users/<USER>/Documents/workspace/local_repo_xm_xydpt_v1.1.2"/>
    <property name="java.class.version" value="52.0"/>
    <property name="socksNonProxyHosts" value="127.0.0.1|***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|localhost|*.localhost|local|*.local|crashlytics.com|*.crashlytics.com|&lt;local&gt;|*.&lt;local&gt;"/>
    <property name="CONSOLE_LOG_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%logger{36}] - %msg%n"/>
  </properties>
  <testcase name="testDotEscaping" classname="com.netty.smtp.protocol.SmtpDataProcessorTest" time="0.0"/>
  <testcase name="testDataInterruption" classname="com.netty.smtp.protocol.SmtpDataProcessorTest" time="0.001"/>
  <testcase name="testMessageSizeLimit" classname="com.netty.smtp.protocol.SmtpDataProcessorTest" time="0.001"/>
  <testcase name="testDataTransmissionStatus" classname="com.netty.smtp.protocol.SmtpDataProcessorTest" time="0.0"/>
  <testcase name="testProcessDataLine" classname="com.netty.smtp.protocol.SmtpDataProcessorTest" time="0.001"/>
  <testcase name="testProcessDataLineWrongState" classname="com.netty.smtp.protocol.SmtpDataProcessorTest" time="0.0"/>
  <testcase name="testCompleteMessageTransmission" classname="com.netty.smtp.protocol.SmtpDataProcessorTest" time="0.0"/>
  <testcase name="testMultiLineDataProcessing" classname="com.netty.smtp.protocol.SmtpDataProcessorTest" time="0.001"/>
  <testcase name="testDataEndMarker" classname="com.netty.smtp.protocol.SmtpDataProcessorTest" time="0.001"/>
  <testcase name="testEmptyMessage" classname="com.netty.smtp.protocol.SmtpDataProcessorTest" time="0.001"/>
</testsuite>