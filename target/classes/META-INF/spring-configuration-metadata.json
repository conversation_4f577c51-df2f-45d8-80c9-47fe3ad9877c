{"groups": [{"name": "smtp", "type": "com.netty.smtp.config.SmtpServerProperties", "sourceType": "com.netty.smtp.config.SmtpServerProperties"}, {"name": "smtp.domains", "type": "com.netty.smtp.config.SmtpServerProperties$Domains", "sourceType": "com.netty.smtp.config.SmtpServerProperties", "sourceMethod": "public com.netty.smtp.config.SmtpServerProperties.Domains getDomains() "}, {"name": "smtp.mail", "type": "com.netty.smtp.config.SmtpServerProperties$Mail", "sourceType": "com.netty.smtp.config.SmtpServerProperties", "sourceMethod": "public com.netty.smtp.config.SmtpServerProperties.Mail getMail() "}, {"name": "smtp.relay", "type": "com.netty.smtp.config.SmtpServerProperties$Relay", "sourceType": "com.netty.smtp.config.SmtpServerProperties", "sourceMethod": "public com.netty.smtp.config.SmtpServerProperties.Relay getRelay() "}, {"name": "smtp.relay.auth", "type": "com.netty.smtp.config.SmtpServerProperties$Relay$Auth", "sourceType": "com.netty.smtp.config.SmtpServerProperties$Relay", "sourceMethod": "public com.netty.smtp.config.SmtpServerProperties.Relay.Auth getAuth() "}, {"name": "smtp.relay.retry", "type": "com.netty.smtp.config.SmtpServerProperties$Relay$Retry", "sourceType": "com.netty.smtp.config.SmtpServerProperties$Relay", "sourceMethod": "public com.netty.smtp.config.SmtpServerProperties.Relay.Retry getRetry() "}, {"name": "smtp.server", "type": "com.netty.smtp.config.SmtpServerProperties$Server", "sourceType": "com.netty.smtp.config.SmtpServerProperties", "sourceMethod": "public com.netty.smtp.config.SmtpServerProperties.Server getServer() "}], "properties": [{"name": "smtp.domains.local", "type": "java.util.List<java.lang.String>", "sourceType": "com.netty.smtp.config.SmtpServerProperties$Domains"}, {"name": "smtp.domains.routing", "type": "java.util.Map<java.lang.String,java.lang.String>", "sourceType": "com.netty.smtp.config.SmtpServerProperties$Domains"}, {"name": "smtp.mail.max-recipients", "type": "java.lang.Integer", "sourceType": "com.netty.smtp.config.SmtpServerProperties$Mail"}, {"name": "smtp.mail.max-size", "type": "java.lang.Integer", "sourceType": "com.netty.smtp.config.SmtpServerProperties$Mail"}, {"name": "smtp.mail.queue-size", "type": "java.lang.Integer", "sourceType": "com.netty.smtp.config.SmtpServerProperties$Mail"}, {"name": "smtp.mail.worker-threads", "type": "java.lang.Integer", "sourceType": "com.netty.smtp.config.SmtpServerProperties$Mail"}, {"name": "smtp.relay.auth.password", "type": "java.lang.String", "sourceType": "com.netty.smtp.config.SmtpServerProperties$Relay$Auth"}, {"name": "smtp.relay.auth.username", "type": "java.lang.String", "sourceType": "com.netty.smtp.config.SmtpServerProperties$Relay$Auth"}, {"name": "smtp.relay.default-host", "type": "java.lang.String", "sourceType": "com.netty.smtp.config.SmtpServerProperties$Relay"}, {"name": "smtp.relay.default-port", "type": "java.lang.Integer", "sourceType": "com.netty.smtp.config.SmtpServerProperties$Relay"}, {"name": "smtp.relay.enabled", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.netty.smtp.config.SmtpServerProperties$Relay"}, {"name": "smtp.relay.retry.delay-seconds", "type": "java.lang.Integer", "sourceType": "com.netty.smtp.config.SmtpServerProperties$Relay$Retry"}, {"name": "smtp.relay.retry.max-attempts", "type": "java.lang.Integer", "sourceType": "com.netty.smtp.config.SmtpServerProperties$Relay$Retry"}, {"name": "smtp.server.connection-timeout", "type": "java.lang.Integer", "sourceType": "com.netty.smtp.config.SmtpServerProperties$Server"}, {"name": "smtp.server.hostname", "type": "java.lang.String", "sourceType": "com.netty.smtp.config.SmtpServerProperties$Server"}, {"name": "smtp.server.max-connections", "type": "java.lang.Integer", "sourceType": "com.netty.smtp.config.SmtpServerProperties$Server"}, {"name": "smtp.server.port", "type": "java.lang.Integer", "sourceType": "com.netty.smtp.config.SmtpServerProperties$Server"}, {"name": "smtp.server.ssl-enabled", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.netty.smtp.config.SmtpServerProperties$Server"}, {"name": "smtp.server.ssl-port", "type": "java.lang.Integer", "sourceType": "com.netty.smtp.config.SmtpServerProperties$Server"}, {"name": "smtp.server.starttls-enabled", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.netty.smtp.config.SmtpServerProperties$Server"}], "hints": []}