package com.netty.smtp.service;

import com.netty.smtp.model.EmailMessage;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.io.TempDir;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 本地邮件投递服务测试
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
class LocalDeliveryServiceTest {
    
    private LocalDeliveryService deliveryService;
    
    @TempDir
    Path tempDir;
    
    @BeforeEach
    void setUp() {
        deliveryService = new LocalDeliveryService();
        // 设置临时目录作为邮箱目录
        System.setProperty("user.dir", tempDir.toString());
    }
    
    @Test
    @DisplayName("测试单个收件人邮件投递")
    void testDeliverToSingleRecipient() throws IOException {
        EmailMessage message = createTestMessage();
        List<String> recipients = Arrays.asList("<EMAIL>");
        
        deliveryService.deliverMessage(message, recipients);
        
        // 验证邮件文件是否创建
        Path mailboxPath = deliveryService.getMailboxPath("<EMAIL>");
        assertTrue(Files.exists(mailboxPath));
        
        List<Path> messages = deliveryService.listMessages("<EMAIL>");
        assertEquals(1, messages.size());
        
        String content = deliveryService.readMessage(messages.get(0));
        assertTrue(content.contains("Subject: Test Message"));
        assertTrue(content.contains("This is a test message"));
    }
    
    @Test
    @DisplayName("测试多个收件人邮件投递")
    void testDeliverToMultipleRecipients() throws IOException {
        EmailMessage message = createTestMessage();
        List<String> recipients = Arrays.asList("<EMAIL>", "<EMAIL>", "<EMAIL>");
        
        deliveryService.deliverMessage(message, recipients);
        
        // 验证每个收件人都收到了邮件
        for (String recipient : recipients) {
            Path mailboxPath = deliveryService.getMailboxPath(recipient);
            assertTrue(Files.exists(mailboxPath));
            
            List<Path> messages = deliveryService.listMessages(recipient);
            assertEquals(1, messages.size());
            
            String content = deliveryService.readMessage(messages.get(0));
            assertTrue(content.contains("Delivered-To: " + recipient));
            assertTrue(content.contains("Subject: Test Message"));
        }
    }
    
    @Test
    @DisplayName("测试邮箱统计信息")
    void testMailboxStats() throws IOException {
        EmailMessage message1 = createTestMessage();
        EmailMessage message2 = createTestMessage();
        message2.setSubject("Second Test Message");
        
        String recipient = "<EMAIL>";
        
        deliveryService.deliverMessage(message1, Arrays.asList(recipient));
        deliveryService.deliverMessage(message2, Arrays.asList(recipient));
        
        LocalDeliveryService.MailboxStats stats = deliveryService.getMailboxStats(recipient);
        
        assertEquals(2, stats.getMessageCount());
        assertTrue(stats.getTotalSize() > 0);
    }
    
    @Test
    @DisplayName("测试邮件删除")
    void testDeleteMessage() throws IOException {
        EmailMessage message = createTestMessage();
        String recipient = "<EMAIL>";
        
        deliveryService.deliverMessage(message, Arrays.asList(recipient));
        
        List<Path> messages = deliveryService.listMessages(recipient);
        assertEquals(1, messages.size());
        
        boolean deleted = deliveryService.deleteMessage(messages.get(0));
        assertTrue(deleted);
        
        List<Path> remainingMessages = deliveryService.listMessages(recipient);
        assertEquals(0, remainingMessages.size());
    }
    
    @Test
    @DisplayName("测试不存在的邮箱")
    void testNonExistentMailbox() throws IOException {
        List<Path> messages = deliveryService.listMessages("<EMAIL>");
        assertTrue(messages.isEmpty());
        
        LocalDeliveryService.MailboxStats stats = deliveryService.getMailboxStats("<EMAIL>");
        assertEquals(0, stats.getMessageCount());
        assertEquals(0, stats.getTotalSize());
    }
    
    @Test
    @DisplayName("测试邮箱名称清理")
    void testMailboxNameSanitization() throws IOException {
        EmailMessage message = createTestMessage();
        String recipient = "test@exam<ple>.com"; // 包含特殊字符
        
        deliveryService.deliverMessage(message, Arrays.asList(recipient));
        
        Path mailboxPath = deliveryService.getMailboxPath(recipient);
        assertTrue(Files.exists(mailboxPath));
        
        // 验证路径名称已被清理
        String pathName = mailboxPath.getFileName().toString();
        assertFalse(pathName.contains("<"));
        assertFalse(pathName.contains(">"));
    }
    
    @Test
    @DisplayName("测试完整邮件格式")
    void testCompleteMessageFormat() throws IOException {
        EmailMessage message = new EmailMessage("test-session");
        message.setFrom("<EMAIL>");
        message.setSubject("Complete Format Test");
        message.setRawData("From: <EMAIL>\r\n" +
                          "To: <EMAIL>\r\n" +
                          "Subject: Complete Format Test\r\n" +
                          "Date: Mon, 1 Jan 2024 12:00:00 +0000\r\n" +
                          "\r\n" +
                          "This is the message body.\r\n" +
                          "With multiple lines.\r\n");
        
        String recipient = "<EMAIL>";
        deliveryService.deliverMessage(message, Arrays.asList(recipient));
        
        List<Path> messages = deliveryService.listMessages(recipient);
        assertEquals(1, messages.size());
        
        String content = deliveryService.readMessage(messages.get(0));
        
        // 验证投递头部
        assertTrue(content.contains("Delivered-To: " + recipient));
        assertTrue(content.contains("Return-Path: <<EMAIL>>"));
        assertTrue(content.contains("Received: from localhost"));
        
        // 验证原始内容
        assertTrue(content.contains("From: <EMAIL>"));
        assertTrue(content.contains("Subject: Complete Format Test"));
        assertTrue(content.contains("This is the message body."));
        assertTrue(content.contains("With multiple lines."));
    }
    
    @Test
    @DisplayName("测试空邮件内容")
    void testEmptyMessageContent() throws IOException {
        EmailMessage message = new EmailMessage("test-session");
        message.setFrom("<EMAIL>");
        message.setSubject("Empty Content Test");
        // 不设置rawData和body
        
        String recipient = "<EMAIL>";
        deliveryService.deliverMessage(message, Arrays.asList(recipient));
        
        List<Path> messages = deliveryService.listMessages(recipient);
        assertEquals(1, messages.size());
        
        String content = deliveryService.readMessage(messages.get(0));
        
        // 验证基本头部被添加
        assertTrue(content.contains("From: <EMAIL>"));
        assertTrue(content.contains("To: " + recipient));
        assertTrue(content.contains("Subject: Empty Content Test"));
        assertTrue(content.contains("Message-ID:"));
    }
    
    @Test
    @DisplayName("测试多次投递到同一邮箱")
    void testMultipleDeliveriesToSameMailbox() throws IOException {
        String recipient = "<EMAIL>";
        
        for (int i = 1; i <= 3; i++) {
            EmailMessage message = createTestMessage();
            message.setSubject("Test Message " + i);
            deliveryService.deliverMessage(message, Arrays.asList(recipient));
        }
        
        List<Path> messages = deliveryService.listMessages(recipient);
        assertEquals(3, messages.size());
        
        LocalDeliveryService.MailboxStats stats = deliveryService.getMailboxStats(recipient);
        assertEquals(3, stats.getMessageCount());
        
        // 验证每个邮件都有不同的内容
        for (int i = 0; i < messages.size(); i++) {
            String content = deliveryService.readMessage(messages.get(i));
            assertTrue(content.contains("Test Message"));
        }
    }
    
    private EmailMessage createTestMessage() {
        EmailMessage message = new EmailMessage("test-session");
        message.setFrom("<EMAIL>");
        message.setSubject("Test Message");
        message.setBody("This is a test message");
        message.setRawData("Subject: Test Message\r\n\r\nThis is a test message");
        return message;
    }
}
