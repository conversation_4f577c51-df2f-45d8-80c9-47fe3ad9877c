package com.netty.smtp.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.net.Socket;
import java.util.Arrays;
import java.util.List;

/**
 * SMTP测试客户端工具
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class SmtpTestClient implements AutoCloseable {
    
    private static final Logger logger = LoggerFactory.getLogger(SmtpTestClient.class);
    
    private final String host;
    private final int port;
    private Socket socket;
    private BufferedReader reader;
    private PrintWriter writer;
    private boolean connected = false;
    
    public SmtpTestClient(String host, int port) {
        this.host = host;
        this.port = port;
    }
    
    /**
     * 连接到SMTP服务器
     */
    public void connect() throws IOException {
        if (connected) {
            throw new IllegalStateException("Already connected");
        }
        
        logger.info("Connecting to SMTP server {}:{}", host, port);
        
        socket = new Socket(host, port);
        reader = new BufferedReader(new InputStreamReader(socket.getInputStream()));
        writer = new PrintWriter(socket.getOutputStream(), true);
        
        // 读取欢迎消息
        String welcome = readResponse();
        logger.info("Server welcome: {}", welcome);
        
        if (!welcome.startsWith("220")) {
            throw new IOException("Unexpected welcome message: " + welcome);
        }
        
        connected = true;
    }
    
    /**
     * 发送HELO命令
     */
    public String helo(String hostname) throws IOException {
        return sendCommand("HELO " + hostname);
    }
    
    /**
     * 发送EHLO命令
     */
    public String ehlo(String hostname) throws IOException {
        return sendCommand("EHLO " + hostname);
    }
    
    /**
     * 发送MAIL FROM命令
     */
    public String mailFrom(String from) throws IOException {
        return sendCommand("MAIL FROM:<" + from + ">");
    }
    
    /**
     * 发送RCPT TO命令
     */
    public String rcptTo(String to) throws IOException {
        return sendCommand("RCPT TO:<" + to + ">");
    }
    
    /**
     * 发送DATA命令并传输邮件内容
     */
    public String sendData(String messageContent) throws IOException {
        String dataResponse = sendCommand("DATA");
        if (!dataResponse.startsWith("354")) {
            return dataResponse;
        }
        
        // 发送邮件内容
        String[] lines = messageContent.split("\n");
        for (String line : lines) {
            // 处理点号转义
            if (line.startsWith(".")) {
                line = "." + line;
            }
            writer.println(line);
        }
        
        // 发送结束标记
        writer.println(".");
        
        return readResponse();
    }
    
    /**
     * 发送RSET命令
     */
    public String reset() throws IOException {
        return sendCommand("RSET");
    }
    
    /**
     * 发送NOOP命令
     */
    public String noop() throws IOException {
        return sendCommand("NOOP");
    }
    
    /**
     * 发送QUIT命令
     */
    public String quit() throws IOException {
        return sendCommand("QUIT");
    }
    
    /**
     * 发送完整的邮件
     */
    public void sendMail(String from, List<String> recipients, String subject, String body) throws IOException {
        if (!connected) {
            throw new IllegalStateException("Not connected");
        }
        
        logger.info("Sending mail from {} to {} recipients", from, recipients.size());
        
        // HELO
        String response = helo("test-client.example.com");
        if (!response.startsWith("250")) {
            throw new IOException("HELO failed: " + response);
        }
        
        // MAIL FROM
        response = mailFrom(from);
        if (!response.startsWith("250")) {
            throw new IOException("MAIL FROM failed: " + response);
        }
        
        // RCPT TO
        for (String recipient : recipients) {
            response = rcptTo(recipient);
            if (!response.startsWith("250")) {
                throw new IOException("RCPT TO failed for " + recipient + ": " + response);
            }
        }
        
        // 构建邮件内容
        StringBuilder messageContent = new StringBuilder();
        messageContent.append("From: ").append(from).append("\n");
        messageContent.append("To: ").append(String.join(", ", recipients)).append("\n");
        messageContent.append("Subject: ").append(subject).append("\n");
        messageContent.append("\n");
        messageContent.append(body);
        
        // DATA
        response = sendData(messageContent.toString());
        if (!response.startsWith("250")) {
            throw new IOException("DATA failed: " + response);
        }
        
        logger.info("Mail sent successfully: {}", response);
    }
    
    /**
     * 发送命令并读取响应
     */
    private String sendCommand(String command) throws IOException {
        if (!connected) {
            throw new IllegalStateException("Not connected");
        }
        
        logger.debug("Sending command: {}", command);
        writer.println(command);
        
        String response = readResponse();
        logger.debug("Received response: {}", response);
        
        return response;
    }
    
    /**
     * 读取服务器响应
     */
    private String readResponse() throws IOException {
        String line = reader.readLine();
        if (line == null) {
            throw new IOException("Connection closed by server");
        }
        return line;
    }
    
    /**
     * 检查是否已连接
     */
    public boolean isConnected() {
        return connected && socket != null && !socket.isClosed();
    }
    
    @Override
    public void close() throws IOException {
        if (connected) {
            try {
                quit();
            } catch (IOException e) {
                logger.warn("Error sending QUIT command", e);
            }
            
            if (socket != null) {
                socket.close();
            }
            
            connected = false;
            logger.info("Disconnected from SMTP server");
        }
    }
    
    /**
     * 创建测试邮件内容
     */
    public static String createTestMessage(String subject, String body) {
        return "Subject: " + subject + "\n" +
               "Content-Type: text/plain; charset=UTF-8\n" +
               "\n" +
               body;
    }
    
    /**
     * 创建HTML测试邮件内容
     */
    public static String createHtmlTestMessage(String subject, String htmlBody) {
        return "Subject: " + subject + "\n" +
               "Content-Type: text/html; charset=UTF-8\n" +
               "\n" +
               htmlBody;
    }
    
    /**
     * 主方法用于手动测试
     */
    public static void main(String[] args) {
        try (SmtpTestClient client = new SmtpTestClient("localhost", 2525)) {
            client.connect();
            
            client.sendMail(
                "<EMAIL>",
                Arrays.asList("recipient1@localhost", "recipient2@localhost"),
                "Test Message from SMTP Client",
                "This is a test message sent using the SMTP test client.\n\n" +
                "Best regards,\nSMTP Test Client"
            );
            
            System.out.println("Test email sent successfully!");
            
        } catch (Exception e) {
            System.err.println("Error: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
