package com.netty.smtp.protocol;

import com.netty.smtp.model.SmtpResponse;
import com.netty.smtp.model.SmtpSession;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import static org.junit.jupiter.api.Assertions.*;

/**
 * SMTP命令处理器测试
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
class SmtpCommandProcessorTest {
    
    private SmtpCommandProcessor processor;
    private SmtpSession session;
    
    @BeforeEach
    void setUp() {
        processor = new SmtpCommandProcessor();
        processor.setServerHostname("test.localhost");
        processor.setMaxRecipients(10);
        processor.setMaxMessageSize(1024 * 1024); // 1MB
        
        session = new SmtpSession("127.0.0.1");
    }
    
    @Test
    @DisplayName("测试HELO命令")
    void testHeloCommand() {
        SmtpResponse response = processor.processCommand("HELO client.example.com", session);
        
        assertTrue(response.isSuccess());
        assertEquals(250, response.getCode());
        assertEquals(SmtpSession.State.GREETED, session.getState());
        assertEquals("client.example.com", session.getClientHostname());
        assertFalse(session.isExtendedSmtp());
    }
    
    @Test
    @DisplayName("测试EHLO命令")
    void testEhloCommand() {
        SmtpResponse response = processor.processCommand("EHLO client.example.com", session);
        
        assertTrue(response.isSuccess());
        assertEquals(250, response.getCode());
        assertEquals(SmtpSession.State.GREETED, session.getState());
        assertEquals("client.example.com", session.getClientHostname());
        assertTrue(session.isExtendedSmtp());
        assertTrue(response.isMultiLine());
    }
    
    @Test
    @DisplayName("测试MAIL FROM命令")
    void testMailFromCommand() {
        // 先执行HELO
        processor.processCommand("HELO client.example.com", session);
        
        SmtpResponse response = processor.processCommand("MAIL FROM:<<EMAIL>>", session);
        
        assertTrue(response.isSuccess());
        assertEquals(250, response.getCode());
        assertEquals(SmtpSession.State.MAIL_STARTED, session.getState());
        assertEquals("<EMAIL>", session.getMailFrom());
    }
    
    @Test
    @DisplayName("测试MAIL FROM命令 - 错误序列")
    void testMailFromCommandBadSequence() {
        // 没有先执行HELO
        SmtpResponse response = processor.processCommand("MAIL FROM:<<EMAIL>>", session);
        
        assertFalse(response.isSuccess());
        assertEquals(503, response.getCode());
    }
    
    @Test
    @DisplayName("测试MAIL FROM命令 - 无效邮件地址")
    void testMailFromCommandInvalidAddress() {
        processor.processCommand("HELO client.example.com", session);
        
        SmtpResponse response = processor.processCommand("MAIL FROM:<invalid-email>", session);
        
        assertFalse(response.isSuccess());
        assertEquals(553, response.getCode());
    }
    
    @Test
    @DisplayName("测试RCPT TO命令")
    void testRcptToCommand() {
        // 准备会话状态
        processor.processCommand("HELO client.example.com", session);
        processor.processCommand("MAIL FROM:<<EMAIL>>", session);
        
        SmtpResponse response = processor.processCommand("RCPT TO:<<EMAIL>>", session);
        
        assertTrue(response.isSuccess());
        assertEquals(250, response.getCode());
        assertEquals(SmtpSession.State.RCPT_ADDED, session.getState());
        assertTrue(session.getRecipients().contains("<EMAIL>"));
    }
    
    @Test
    @DisplayName("测试RCPT TO命令 - 多个收件人")
    void testRcptToCommandMultipleRecipients() {
        // 准备会话状态
        processor.processCommand("HELO client.example.com", session);
        processor.processCommand("MAIL FROM:<<EMAIL>>", session);
        
        // 添加多个收件人
        processor.processCommand("RCPT TO:<<EMAIL>>", session);
        processor.processCommand("RCPT TO:<<EMAIL>>", session);
        processor.processCommand("RCPT TO:<<EMAIL>>", session);
        
        assertEquals(3, session.getRecipients().size());
        assertTrue(session.getRecipients().contains("<EMAIL>"));
        assertTrue(session.getRecipients().contains("<EMAIL>"));
        assertTrue(session.getRecipients().contains("<EMAIL>"));
    }
    
    @Test
    @DisplayName("测试RCPT TO命令 - 收件人数量限制")
    void testRcptToCommandTooManyRecipients() {
        processor.setMaxRecipients(2);
        
        // 准备会话状态
        processor.processCommand("HELO client.example.com", session);
        processor.processCommand("MAIL FROM:<<EMAIL>>", session);
        
        // 添加收件人直到超过限制
        processor.processCommand("RCPT TO:<<EMAIL>>", session);
        processor.processCommand("RCPT TO:<<EMAIL>>", session);
        SmtpResponse response = processor.processCommand("RCPT TO:<<EMAIL>>", session);
        
        assertFalse(response.isSuccess());
        assertEquals(452, response.getCode());
        assertEquals(2, session.getRecipients().size());
    }
    
    @Test
    @DisplayName("测试DATA命令")
    void testDataCommand() {
        // 准备会话状态
        processor.processCommand("HELO client.example.com", session);
        processor.processCommand("MAIL FROM:<<EMAIL>>", session);
        processor.processCommand("RCPT TO:<<EMAIL>>", session);
        
        SmtpResponse response = processor.processCommand("DATA", session);
        
        assertTrue(response.isSuccess());
        assertEquals(354, response.getCode());
        assertEquals(SmtpSession.State.DATA_STARTED, session.getState());
    }
    
    @Test
    @DisplayName("测试DATA命令 - 没有收件人")
    void testDataCommandNoRecipients() {
        // 准备会话状态但不添加收件人
        processor.processCommand("HELO client.example.com", session);
        processor.processCommand("MAIL FROM:<<EMAIL>>", session);
        
        SmtpResponse response = processor.processCommand("DATA", session);
        
        assertFalse(response.isSuccess());
        assertEquals(503, response.getCode());
    }
    
    @Test
    @DisplayName("测试RSET命令")
    void testRsetCommand() {
        // 准备会话状态
        processor.processCommand("HELO client.example.com", session);
        processor.processCommand("MAIL FROM:<<EMAIL>>", session);
        processor.processCommand("RCPT TO:<<EMAIL>>", session);
        
        SmtpResponse response = processor.processCommand("RSET", session);
        
        assertTrue(response.isSuccess());
        assertEquals(250, response.getCode());
        assertEquals(SmtpSession.State.GREETED, session.getState());
        assertNull(session.getMailFrom());
        assertTrue(session.getRecipients().isEmpty());
    }
    
    @Test
    @DisplayName("测试NOOP命令")
    void testNoopCommand() {
        SmtpResponse response = processor.processCommand("NOOP", session);
        
        assertTrue(response.isSuccess());
        assertEquals(250, response.getCode());
    }
    
    @Test
    @DisplayName("测试QUIT命令")
    void testQuitCommand() {
        SmtpResponse response = processor.processCommand("QUIT", session);
        
        assertTrue(response.isSuccess());
        assertEquals(221, response.getCode());
        assertEquals(SmtpSession.State.QUIT, session.getState());
    }
    
    @Test
    @DisplayName("测试VRFY命令")
    void testVrfyCommand() {
        SmtpResponse response = processor.processCommand("VRFY <EMAIL>", session);
        
        assertTrue(response.isSuccess());
        assertEquals(252, response.getCode());
    }
    
    @Test
    @DisplayName("测试HELP命令")
    void testHelpCommand() {
        SmtpResponse response = processor.processCommand("HELP", session);
        
        assertTrue(response.isSuccess());
        assertEquals(214, response.getCode());
        assertTrue(response.isMultiLine());
    }
    
    @Test
    @DisplayName("测试未知命令")
    void testUnknownCommand() {
        SmtpResponse response = processor.processCommand("UNKNOWN", session);
        
        assertFalse(response.isSuccess());
        assertEquals(500, response.getCode());
    }
    
    @Test
    @DisplayName("测试空命令")
    void testEmptyCommand() {
        SmtpResponse response = processor.processCommand("", session);
        
        assertFalse(response.isSuccess());
        assertEquals(500, response.getCode());
    }
    
    @Test
    @DisplayName("测试命令参数错误")
    void testCommandParameterError() {
        processor.processCommand("HELO client.example.com", session);
        
        // MAIL FROM命令格式错误
        SmtpResponse response = processor.processCommand("MAIL FROM:invalid-format", session);
        
        assertFalse(response.isSuccess());
        assertEquals(501, response.getCode());
    }
}
