package com.netty.smtp.protocol;

import com.netty.smtp.model.SmtpResponse;
import com.netty.smtp.model.SmtpSession;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import static org.junit.jupiter.api.Assertions.*;

/**
 * SMTP数据处理器测试
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
class SmtpDataProcessorTest {
    
    private SmtpDataProcessor processor;
    private SmtpSession session;
    
    @BeforeEach
    void setUp() {
        processor = new SmtpDataProcessor();
        processor.setMaxMessageSize(1024); // 1KB for testing
        
        session = new SmtpSession("127.0.0.1");
        session.setState(SmtpSession.State.DATA_STARTED);
        session.setMailFrom("<EMAIL>");
        session.addRecipient("<EMAIL>");
    }
    
    @Test
    @DisplayName("测试数据行处理")
    void testProcessDataLine() {
        SmtpResponse response = processor.processDataLine("Subject: Test Message", session);
        
        assertNull(response); // 继续接收数据
        assertEquals(SmtpSession.State.DATA_RECEIVING, session.getState());
        assertTrue(session.getCompleteMessage().contains("Subject: Test Message"));
    }
    
    @Test
    @DisplayName("测试数据结束标记")
    void testDataEndMarker() {
        // 添加一些数据
        processor.processDataLine("Subject: Test Message", session);
        processor.processDataLine("", session);
        processor.processDataLine("This is the message body.", session);
        
        // 发送结束标记
        SmtpResponse response = processor.processDataLine(".", session);
        
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertEquals(250, response.getCode());
        assertEquals(SmtpSession.State.COMPLETED, session.getState());
        assertTrue(response.getMessage().contains("Message accepted"));
    }
    
    @Test
    @DisplayName("测试点号转义")
    void testDotEscaping() {
        // 测试以双点号开头的行
        SmtpResponse response = processor.processDataLine("..This line starts with a dot", session);
        
        assertNull(response);
        assertTrue(session.getCompleteMessage().contains(".This line starts with a dot"));
    }
    
    @Test
    @DisplayName("测试消息大小限制")
    void testMessageSizeLimit() {
        // 创建超过大小限制的数据
        StringBuilder largeData = new StringBuilder();
        for (int i = 0; i < 200; i++) {
            largeData.append("This is a long line of text to exceed the message size limit. ");
        }
        
        SmtpResponse response = processor.processDataLine(largeData.toString(), session);
        
        assertNotNull(response);
        assertFalse(response.isSuccess());
        assertEquals(552, response.getCode());
        assertTrue(response.getMessage().contains("Message size exceeds"));
    }
    
    @Test
    @DisplayName("测试错误状态下的数据处理")
    void testProcessDataLineWrongState() {
        session.setState(SmtpSession.State.GREETED);
        
        SmtpResponse response = processor.processDataLine("Some data", session);
        
        assertNotNull(response);
        assertFalse(response.isSuccess());
        assertEquals(503, response.getCode());
    }
    
    @Test
    @DisplayName("测试数据传输中断")
    void testDataInterruption() {
        // 添加一些数据
        processor.processDataLine("Subject: Test Message", session);
        
        SmtpResponse response = processor.handleDataInterruption(session, "Connection timeout");
        
        assertNotNull(response);
        assertFalse(response.isSuccess());
        assertEquals(554, response.getCode());
        assertEquals(SmtpSession.State.GREETED, session.getState());
        assertEquals(0, session.getMessageSize());
    }
    
    @Test
    @DisplayName("测试完整邮件传输")
    void testCompleteMessageTransmission() {
        // 模拟完整的邮件传输
        processor.processDataLine("From: <EMAIL>", session);
        processor.processDataLine("To: <EMAIL>", session);
        processor.processDataLine("Subject: Test Message", session);
        processor.processDataLine("", session);
        processor.processDataLine("This is the message body.", session);
        processor.processDataLine("", session);
        processor.processDataLine("Best regards,", session);
        processor.processDataLine("Sender", session);
        
        // 在发送结束标记前，先检查消息内容
        String messageBeforeEnd = session.getCompleteMessage();
        assertTrue(messageBeforeEnd.contains("From: <EMAIL>"));
        assertTrue(messageBeforeEnd.contains("To: <EMAIL>"));
        assertTrue(messageBeforeEnd.contains("Subject: Test Message"));
        assertTrue(messageBeforeEnd.contains("This is the message body."));
        assertTrue(messageBeforeEnd.contains("Best regards,"));

        SmtpResponse response = processor.processDataLine(".", session);

        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertEquals(250, response.getCode());
        assertEquals(SmtpSession.State.COMPLETED, session.getState());
    }
    
    @Test
    @DisplayName("测试空邮件")
    void testEmptyMessage() {
        SmtpResponse response = processor.processDataLine(".", session);
        
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertEquals(250, response.getCode());
    }
    
    @Test
    @DisplayName("测试数据传输状态信息")
    void testDataTransmissionStatus() {
        processor.processDataLine("Subject: Test Message", session);
        
        String status = processor.getDataTransmissionStatus(session);
        
        assertNotNull(status);
        assertTrue(status.contains(session.getSessionId()));
        assertTrue(status.contains("DATA_RECEIVING"));
        assertTrue(status.contains("Recipients: 1"));
    }
    
    @Test
    @DisplayName("测试多行数据处理")
    void testMultiLineDataProcessing() {
        String[] lines = {
            "From: <EMAIL>",
            "To: <EMAIL>",
            "Subject: Multi-line Test",
            "Content-Type: text/plain",
            "",
            "This is line 1",
            "This is line 2",
            "This is line 3",
            "",
            "End of message"
        };
        
        for (String line : lines) {
            SmtpResponse response = processor.processDataLine(line, session);
            assertNull(response, "Should continue receiving data for line: " + line);
        }

        // 在发送结束标记前检查消息内容
        String messageBeforeEnd = session.getCompleteMessage();
        for (String line : lines) {
            assertTrue(messageBeforeEnd.contains(line), "Message should contain line: " + line);
        }

        SmtpResponse finalResponse = processor.processDataLine(".", session);
        assertNotNull(finalResponse);
        assertTrue(finalResponse.isSuccess());
    }
}
