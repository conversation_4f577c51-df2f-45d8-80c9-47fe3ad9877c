package com.netty.smtp.integration;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.AfterEach;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import java.io.*;
import java.net.Socket;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * SMTP服务器集成测试
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@TestPropertySource(properties = {
    "smtp.server.port=2526",
    "smtp.server.hostname=test.localhost",
    "logging.level.com.netty.smtp=DEBUG"
})
class SmtpServerIntegrationTest {
    
    private static final String SMTP_HOST = "localhost";
    private static final int SMTP_PORT = 2526;
    
    private Socket socket;
    private BufferedReader reader;
    private PrintWriter writer;
    
    @BeforeEach
    void setUp() throws Exception {
        // 等待服务器启动
        Thread.sleep(2000);
        
        // 连接到SMTP服务器
        socket = new Socket(SMTP_HOST, SMTP_PORT);
        reader = new BufferedReader(new InputStreamReader(socket.getInputStream()));
        writer = new PrintWriter(socket.getOutputStream(), true);
        
        // 读取欢迎消息
        String welcome = reader.readLine();
        assertTrue(welcome.startsWith("220"), "Expected welcome message, got: " + welcome);
    }
    
    @AfterEach
    void tearDown() throws Exception {
        if (socket != null && !socket.isClosed()) {
            writer.println("QUIT");
            String response = reader.readLine();
            assertTrue(response.startsWith("221"));
            
            socket.close();
        }
    }
    
    @Test
    @DisplayName("测试基本SMTP会话")
    void testBasicSmtpSession() throws IOException {
        // HELO
        writer.println("HELO client.test.com");
        String response = reader.readLine();
        assertTrue(response.startsWith("250"), "HELO failed: " + response);
        
        // MAIL FROM
        writer.println("MAIL FROM:<<EMAIL>>");
        response = reader.readLine();
        assertTrue(response.startsWith("250"), "MAIL FROM failed: " + response);
        
        // RCPT TO
        writer.println("RCPT TO:<recipient@localhost>");
        response = reader.readLine();
        assertTrue(response.startsWith("250"), "RCPT TO failed: " + response);
        
        // DATA
        writer.println("DATA");
        response = reader.readLine();
        assertTrue(response.startsWith("354"), "DATA failed: " + response);
        
        // 邮件内容
        writer.println("Subject: Integration Test");
        writer.println("");
        writer.println("This is a test message from integration test.");
        writer.println(".");
        response = reader.readLine();
        assertTrue(response.startsWith("250"), "Message delivery failed: " + response);
    }
    
    @Test
    @DisplayName("测试EHLO扩展命令")
    void testEhloCommand() throws IOException {
        writer.println("EHLO client.test.com");
        
        String response = reader.readLine();
        assertTrue(response.startsWith("250"), "EHLO failed: " + response);
        
        // 读取扩展功能列表
        StringBuilder extensions = new StringBuilder();
        String line;
        while ((line = reader.readLine()) != null) {
            if (line.startsWith("250 ")) {
                extensions.append(line);
                break;
            } else if (line.startsWith("250-")) {
                extensions.append(line).append("\n");
            }
        }
        
        String extensionList = extensions.toString();
        assertTrue(extensionList.contains("SIZE"), "SIZE extension not found");
        assertTrue(extensionList.contains("8BITMIME"), "8BITMIME extension not found");
        assertTrue(extensionList.contains("PIPELINING"), "PIPELINING extension not found");
    }
    
    @Test
    @DisplayName("测试多收件人邮件发送")
    void testMultipleRecipients() throws IOException {
        // HELO
        writer.println("HELO client.test.com");
        reader.readLine();
        
        // MAIL FROM
        writer.println("MAIL FROM:<<EMAIL>>");
        reader.readLine();
        
        // 多个RCPT TO
        writer.println("RCPT TO:<recipient1@localhost>");
        String response1 = reader.readLine();
        assertTrue(response1.startsWith("250"));
        
        writer.println("RCPT TO:<recipient2@localhost>");
        String response2 = reader.readLine();
        assertTrue(response2.startsWith("250"));
        
        writer.println("RCPT TO:<recipient3@localhost>");
        String response3 = reader.readLine();
        assertTrue(response3.startsWith("250"));
        
        // DATA
        writer.println("DATA");
        String dataResponse = reader.readLine();
        assertTrue(dataResponse.startsWith("354"));
        
        // 邮件内容
        writer.println("Subject: Multi-Recipient Test");
        writer.println("From: <EMAIL>");
        writer.println("To: recipient1@localhost, recipient2@localhost, recipient3@localhost");
        writer.println("");
        writer.println("This message is sent to multiple recipients.");
        writer.println(".");
        
        String finalResponse = reader.readLine();
        assertTrue(finalResponse.startsWith("250"));
    }
    
    @Test
    @DisplayName("测试RSET命令")
    void testRsetCommand() throws IOException {
        // 开始一个邮件事务
        writer.println("HELO client.test.com");
        reader.readLine();
        
        writer.println("MAIL FROM:<<EMAIL>>");
        reader.readLine();
        
        writer.println("RCPT TO:<recipient@localhost>");
        reader.readLine();
        
        // 重置事务
        writer.println("RSET");
        String response = reader.readLine();
        assertTrue(response.startsWith("250"), "RSET failed: " + response);
        
        // 开始新的事务
        writer.println("MAIL FROM:<<EMAIL>>");
        response = reader.readLine();
        assertTrue(response.startsWith("250"), "New transaction after RSET failed: " + response);
    }
    
    @Test
    @DisplayName("测试错误处理")
    void testErrorHandling() throws IOException {
        // 测试错误的命令序列
        writer.println("MAIL FROM:<<EMAIL>>");
        String response = reader.readLine();
        assertTrue(response.startsWith("503"), "Expected bad sequence error: " + response);
        
        // 正确的序列
        writer.println("HELO client.test.com");
        reader.readLine();
        
        // 测试无效的邮件地址
        writer.println("MAIL FROM:<invalid-email>");
        response = reader.readLine();
        assertTrue(response.startsWith("553"), "Expected invalid address error: " + response);
        
        // 测试未知命令
        writer.println("UNKNOWN");
        response = reader.readLine();
        assertTrue(response.startsWith("500"), "Expected unknown command error: " + response);
    }
    
    @Test
    @DisplayName("测试NOOP和HELP命令")
    void testUtilityCommands() throws IOException {
        // NOOP
        writer.println("NOOP");
        String response = reader.readLine();
        assertTrue(response.startsWith("250"), "NOOP failed: " + response);
        
        // HELP
        writer.println("HELP");
        response = reader.readLine();
        assertTrue(response.startsWith("214"), "HELP failed: " + response);
        
        // VRFY
        writer.println("VRFY <EMAIL>");
        response = reader.readLine();
        assertTrue(response.startsWith("252"), "VRFY failed: " + response);
    }
    
    @Test
    @DisplayName("测试大邮件处理")
    void testLargeMessage() throws IOException {
        writer.println("HELO client.test.com");
        reader.readLine();
        
        writer.println("MAIL FROM:<<EMAIL>>");
        reader.readLine();
        
        writer.println("RCPT TO:<recipient@localhost>");
        reader.readLine();
        
        writer.println("DATA");
        reader.readLine();
        
        // 发送较大的邮件内容
        writer.println("Subject: Large Message Test");
        writer.println("");
        
        for (int i = 0; i < 100; i++) {
            writer.println("This is line " + i + " of a large message for testing purposes.");
        }
        
        writer.println(".");
        String response = reader.readLine();
        assertTrue(response.startsWith("250"), "Large message delivery failed: " + response);
    }
    
    @Test
    @DisplayName("测试点号转义")
    void testDotEscaping() throws IOException {
        writer.println("HELO client.test.com");
        reader.readLine();
        
        writer.println("MAIL FROM:<<EMAIL>>");
        reader.readLine();
        
        writer.println("RCPT TO:<recipient@localhost>");
        reader.readLine();
        
        writer.println("DATA");
        reader.readLine();
        
        writer.println("Subject: Dot Escaping Test");
        writer.println("");
        writer.println("This line starts with a single dot:");
        writer.println("..This should be unescaped to a single dot");
        writer.println("Normal line");
        writer.println(".");
        
        String response = reader.readLine();
        assertTrue(response.startsWith("250"), "Dot escaping test failed: " + response);
    }
}
