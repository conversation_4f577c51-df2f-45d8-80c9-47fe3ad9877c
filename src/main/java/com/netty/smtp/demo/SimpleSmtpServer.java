package com.netty.smtp.demo;

import com.netty.smtp.handler.SmtpServerHandler;
import com.netty.smtp.handler.SmtpServerInitializer;
import com.netty.smtp.protocol.SmtpCommandProcessor;
import com.netty.smtp.protocol.SmtpDataProcessor;
import io.netty.bootstrap.ServerBootstrap;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelOption;
import io.netty.channel.EventLoopGroup;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.nio.NioServerSocketChannel;

/**
 * 简单的SMTP服务器演示
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class SimpleSmtpServer {
    
    private static final int PORT = 2525;
    private static final String HOSTNAME = "localhost";
    
    public static void main(String[] args) throws Exception {
        System.out.println("=== 启动 Netty SMTP 服务器 ===");
        System.out.println("端口: " + PORT);
        System.out.println("主机名: " + HOSTNAME);
        System.out.println();
        
        // 创建处理器
        SmtpCommandProcessor commandProcessor = new SmtpCommandProcessor();
        commandProcessor.setServerHostname(HOSTNAME);
        commandProcessor.setMaxRecipients(100);
        commandProcessor.setMaxMessageSize(25 * 1024 * 1024);
        
        SmtpDataProcessor dataProcessor = new SmtpDataProcessor();
        dataProcessor.setMaxMessageSize(25 * 1024 * 1024);
        
        // 创建事件循环组
        EventLoopGroup bossGroup = new NioEventLoopGroup(1);
        EventLoopGroup workerGroup = new NioEventLoopGroup();
        
        try {
            ServerBootstrap bootstrap = new ServerBootstrap();
            bootstrap.group(bossGroup, workerGroup)
                    .channel(NioServerSocketChannel.class)
                    .childHandler(new SmtpServerInitializer(
                            commandProcessor, 
                            dataProcessor, 
                            HOSTNAME, 
                            300))
                    .option(ChannelOption.SO_BACKLOG, 100)
                    .option(ChannelOption.SO_REUSEADDR, true)
                    .childOption(ChannelOption.SO_KEEPALIVE, true)
                    .childOption(ChannelOption.TCP_NODELAY, true);
            
            // 绑定端口并启动服务器
            ChannelFuture future = bootstrap.bind(PORT).sync();
            
            System.out.println("✓ SMTP服务器启动成功！");
            System.out.println("监听地址: " + HOSTNAME + ":" + PORT);
            System.out.println();
            System.out.println("可以使用以下方式测试:");
            System.out.println("1. telnet " + HOSTNAME + " " + PORT);
            System.out.println("2. 运行 SmtpClientDemo 类");
            System.out.println();
            System.out.println("按 Ctrl+C 停止服务器");
            System.out.println("================================");
            
            // 等待服务器关闭
            future.channel().closeFuture().sync();
            
        } finally {
            workerGroup.shutdownGracefully();
            bossGroup.shutdownGracefully();
            System.out.println("SMTP服务器已停止");
        }
    }
}
