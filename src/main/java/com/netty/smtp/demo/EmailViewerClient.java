package com.netty.smtp.demo;

import com.netty.smtp.service.LocalDeliveryService;

import java.io.IOException;
import java.nio.file.*;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 邮件查看客户端 - 查看所有已存在的邮件
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class EmailViewerClient {
    
    private static final String MAILBOX_DIR = "mailboxes";
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    
    private final LocalDeliveryService deliveryService;
    
    public EmailViewerClient() {
        this.deliveryService = new LocalDeliveryService();
    }
    
    public static void main(String[] args) {
        System.out.println("=== 邮件查看客户端 ===");
        System.out.println("扫描目录: " + MAILBOX_DIR);
        System.out.println("================================");
        System.out.println();
        
        EmailViewerClient viewer = new EmailViewerClient();
        viewer.displayAllEmails();
    }
    
    /**
     * 显示所有邮件
     */
    public void displayAllEmails() {
        try {
            Path mailboxPath = Paths.get(MAILBOX_DIR);
            if (!Files.exists(mailboxPath)) {
                System.out.println("📂 邮箱目录不存在: " + MAILBOX_DIR);
                return;
            }
            
            // 收集所有邮件文件
            List<Path> emailFiles = Files.walk(mailboxPath)
                    .filter(path -> path.toString().endsWith(".eml"))
                    .sorted((p1, p2) -> {
                        try {
                            return Files.getLastModifiedTime(p2).compareTo(Files.getLastModifiedTime(p1));
                        } catch (IOException e) {
                            return 0;
                        }
                    })
                    .collect(Collectors.toList());
            
            if (emailFiles.isEmpty()) {
                System.out.println("📭 没有找到任何邮件文件");
                return;
            }
            
            System.out.println("📧 找到 " + emailFiles.size() + " 封邮件");
            System.out.println();
            
            // 显示邮箱统计
            displayMailboxStatistics(emailFiles);
            
            // 显示所有邮件详情
            for (int i = 0; i < emailFiles.size(); i++) {
                Path emailPath = emailFiles.get(i);
                System.out.println("📧 邮件 #" + (i + 1) + " / " + emailFiles.size());
                displayEmailDetails(emailPath);
                
                // 每显示5封邮件后暂停
                if ((i + 1) % 5 == 0 && i + 1 < emailFiles.size()) {
                    System.out.println("⏸️  已显示 " + (i + 1) + " 封邮件，按回车继续查看剩余 " + (emailFiles.size() - i - 1) + " 封邮件...");
                    try {
                        System.in.read();
                    } catch (IOException e) {
                        // 忽略
                    }
                }
            }
            
            System.out.println("✅ 所有邮件显示完成！");
            
        } catch (IOException e) {
            System.err.println("❌ 扫描邮件失败: " + e.getMessage());
        }
    }
    
    /**
     * 显示邮箱统计信息
     */
    private void displayMailboxStatistics(List<Path> emailFiles) {
        Map<String, Integer> mailboxStats = new HashMap<>();
        
        for (Path emailPath : emailFiles) {
            String mailbox = getMailboxFromPath(emailPath);
            mailboxStats.merge(mailbox, 1, Integer::sum);
        }
        
        System.out.println("📊 邮箱统计信息");
        System.out.println("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
        System.out.println("📈 总邮件数: " + emailFiles.size());
        System.out.println("📮 邮箱数量: " + mailboxStats.size());
        System.out.println();
        
        System.out.println("📬 各邮箱邮件数量:");
        mailboxStats.entrySet().stream()
                .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
                .forEach(entry -> 
                    System.out.printf("  %-30s: %3d 封%n", entry.getKey(), entry.getValue())
                );
        
        System.out.println("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
        System.out.println();
    }
    
    /**
     * 显示邮件详情
     */
    private void displayEmailDetails(Path emailPath) {
        try {
            String content = deliveryService.readMessage(emailPath);
            String mailbox = getMailboxFromPath(emailPath);
            String fileName = emailPath.getFileName().toString();
            
            // 获取文件修改时间
            String modifiedTime = DATE_FORMAT.format(new Date(Files.getLastModifiedTime(emailPath).toMillis()));
            
            System.out.println("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
            System.out.println("📬 收件箱: " + mailbox);
            System.out.println("📄 文件名: " + fileName);
            System.out.println("📅 创建时间: " + modifiedTime);
            System.out.println("📍 文件路径: " + emailPath);
            System.out.println("📏 文件大小: " + Files.size(emailPath) + " 字节");
            System.out.println();
            
            // 解析并显示邮件信息
            EmailInfo emailInfo = parseEmail(content);
            displayEmailInfo(emailInfo);
            
            System.out.println("📝 完整邮件内容:");
            System.out.println("─────────────────────────────────────────────────");
            System.out.println(content);
            System.out.println("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
            System.out.println();
            
        } catch (IOException e) {
            System.err.println("❌ 读取邮件失败: " + emailPath + " - " + e.getMessage());
        }
    }
    
    /**
     * 解析邮件信息
     */
    private EmailInfo parseEmail(String content) {
        EmailInfo info = new EmailInfo();
        String[] lines = content.split("\r?\n");
        boolean inHeaders = true;
        StringBuilder body = new StringBuilder();
        
        for (String line : lines) {
            if (inHeaders) {
                if (line.trim().isEmpty()) {
                    inHeaders = false;
                    continue;
                }
                
                int colonIndex = line.indexOf(':');
                if (colonIndex > 0) {
                    String headerName = line.substring(0, colonIndex).trim().toLowerCase();
                    String value = line.substring(colonIndex + 1).trim();
                    
                    switch (headerName) {
                        case "from":
                            info.from = value;
                            break;
                        case "to":
                            info.to = value;
                            break;
                        case "subject":
                            info.subject = value;
                            break;
                        case "date":
                            info.date = value;
                            break;
                        case "message-id":
                            info.messageId = value;
                            break;
                        case "delivered-to":
                            info.deliveredTo = value;
                            break;
                        case "return-path":
                            info.returnPath = value;
                            break;
                    }
                }
            } else {
                body.append(line).append("\n");
            }
        }
        
        info.body = body.toString().trim();
        return info;
    }
    
    /**
     * 显示邮件信息
     */
    private void displayEmailInfo(EmailInfo info) {
        System.out.println("📋 邮件信息摘要:");
        System.out.println("─────────────────────────────────────────────────");
        System.out.printf("%-15s: %s%n", "发件人", info.from != null ? info.from : "(未知)");
        System.out.printf("%-15s: %s%n", "收件人", info.to != null ? info.to : "(未知)");
        System.out.printf("%-15s: %s%n", "主题", info.subject != null ? info.subject : "(无主题)");
        System.out.printf("%-15s: %s%n", "日期", info.date != null ? info.date : "(未知)");
        System.out.printf("%-15s: %s%n", "消息ID", info.messageId != null ? info.messageId : "(未知)");
        System.out.printf("%-15s: %s%n", "投递到", info.deliveredTo != null ? info.deliveredTo : "(未知)");
        System.out.printf("%-15s: %s%n", "返回路径", info.returnPath != null ? info.returnPath : "(未知)");
        System.out.printf("%-15s: %d 字符%n", "正文长度", info.body != null ? info.body.length() : 0);
        System.out.println();
    }
    
    /**
     * 从路径获取邮箱名称
     */
    private String getMailboxFromPath(Path path) {
        Path parent = path.getParent();
        if (parent != null) {
            return parent.getFileName().toString();
        }
        return "unknown";
    }
    
    /**
     * 邮件信息类
     */
    private static class EmailInfo {
        String from;
        String to;
        String subject;
        String date;
        String messageId;
        String deliveredTo;
        String returnPath;
        String body;
    }
}
