package com.netty.smtp.demo;

import java.io.*;
import java.net.Socket;
import java.util.Arrays;
import java.util.List;

/**
 * SMTP客户端演示程序
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class SmtpClientDemo {
    
    private static final String SMTP_HOST = "localhost";
    private static final int SMTP_PORT = 2525;
    
    public static void main(String[] args) {
        System.out.println("=== Netty SMTP Server 演示程序 ===");
        System.out.println();
        
        // 演示SMTP协议交互
        demonstrateSmtpProtocol();
        
        // 演示邮件发送
        demonstrateEmailSending();
        
        System.out.println("=== 演示完成 ===");
    }
    
    /**
     * 演示SMTP协议交互
     */
    private static void demonstrateSmtpProtocol() {
        System.out.println("1. SMTP协议交互演示");
        System.out.println("-------------------");
        
        try (Socket socket = new Socket(SMTP_HOST, SMTP_PORT);
             BufferedReader reader = new BufferedReader(new InputStreamReader(socket.getInputStream()));
             PrintWriter writer = new PrintWriter(socket.getOutputStream(), true)) {
            
            // 读取欢迎消息
            String welcome = reader.readLine();
            System.out.println("S: " + welcome);
            
            // HELO
            System.out.println("C: HELO client.demo.com");
            writer.println("HELO client.demo.com");
            String response = reader.readLine();
            System.out.println("S: " + response);
            
            // MAIL FROM
            System.out.println("C: MAIL FROM:<<EMAIL>>");
            writer.println("MAIL FROM:<<EMAIL>>");
            response = reader.readLine();
            System.out.println("S: " + response);
            
            // RCPT TO
            System.out.println("C: RCPT TO:<recipient@localhost>");
            writer.println("RCPT TO:<recipient@localhost>");
            response = reader.readLine();
            System.out.println("S: " + response);
            
            // DATA
            System.out.println("C: DATA");
            writer.println("DATA");
            response = reader.readLine();
            System.out.println("S: " + response);
            
            // 邮件内容
            System.out.println("C: Subject: SMTP Demo");
            writer.println("Subject: SMTP Demo");
            System.out.println("C: From: <EMAIL>");
            writer.println("From: <EMAIL>");
            System.out.println("C: To: recipient@localhost");
            writer.println("To: recipient@localhost");
            System.out.println("C: ");
            writer.println("");
            System.out.println("C: This is a demonstration of SMTP protocol.");
            writer.println("This is a demonstration of SMTP protocol.");
            System.out.println("C: .");
            writer.println(".");
            response = reader.readLine();
            System.out.println("S: " + response);
            
            // QUIT
            System.out.println("C: QUIT");
            writer.println("QUIT");
            response = reader.readLine();
            System.out.println("S: " + response);
            
            System.out.println("✓ SMTP协议交互成功！");
            
        } catch (Exception e) {
            System.err.println("✗ SMTP协议交互失败: " + e.getMessage());
            System.out.println("请确保SMTP服务器正在运行在 " + SMTP_HOST + ":" + SMTP_PORT);
        }
        
        System.out.println();
    }
    
    /**
     * 演示邮件发送
     */
    private static void demonstrateEmailSending() {
        System.out.println("2. 邮件发送演示");
        System.out.println("---------------");
        
        try {
            SmtpTestClient client = new SmtpTestClient(SMTP_HOST, SMTP_PORT);
            client.connect();
            
            // 发送单个邮件
            System.out.println("发送邮件到单个收件人...");
            client.sendMail(
                "<EMAIL>",
                Arrays.asList("user1@localhost"),
                "测试邮件 - 单个收件人",
                "这是一封测试邮件，发送给单个收件人。\n\n发送时间: " + new java.util.Date()
            );
            System.out.println("✓ 单个收件人邮件发送成功！");
            
            // 发送多收件人邮件
            System.out.println("发送邮件到多个收件人...");
            client.sendMail(
                "<EMAIL>",
                Arrays.asList("user1@localhost", "user2@localhost", "user3@localhost"),
                "测试邮件 - 多个收件人",
                "这是一封测试邮件，发送给多个收件人。\n\n" +
                "收件人列表:\n" +
                "- user1@localhost\n" +
                "- user2@localhost\n" +
                "- user3@localhost\n\n" +
                "发送时间: " + new java.util.Date()
            );
            System.out.println("✓ 多收件人邮件发送成功！");
            
            client.close();
            
        } catch (Exception e) {
            System.err.println("✗ 邮件发送失败: " + e.getMessage());
        }
        
        System.out.println();
    }
    
    /**
     * 简化的SMTP测试客户端
     */
    static class SmtpTestClient implements AutoCloseable {
        private final String host;
        private final int port;
        private Socket socket;
        private BufferedReader reader;
        private PrintWriter writer;
        
        public SmtpTestClient(String host, int port) {
            this.host = host;
            this.port = port;
        }
        
        public void connect() throws IOException {
            socket = new Socket(host, port);
            reader = new BufferedReader(new InputStreamReader(socket.getInputStream()));
            writer = new PrintWriter(socket.getOutputStream(), true);
            
            // 读取欢迎消息
            String welcome = readResponse();
            if (!welcome.startsWith("220")) {
                throw new IOException("Unexpected welcome message: " + welcome);
            }
        }
        
        public void sendMail(String from, List<String> recipients, String subject, String body) throws IOException {
            // HELO
            sendCommand("HELO demo-client.example.com");
            
            // MAIL FROM
            sendCommand("MAIL FROM:<" + from + ">");
            
            // RCPT TO
            for (String recipient : recipients) {
                sendCommand("RCPT TO:<" + recipient + ">");
            }
            
            // DATA
            writer.println("DATA");
            String dataResponse = readResponse();
            if (!dataResponse.startsWith("354")) {
                throw new IOException("DATA command failed: " + dataResponse);
            }
            
            // 邮件内容
            writer.println("From: " + from);
            writer.println("To: " + String.join(", ", recipients));
            writer.println("Subject: " + subject);
            writer.println();
            writer.println(body);
            writer.println(".");
            
            String response = readResponse();
            if (!response.startsWith("250")) {
                throw new IOException("Message delivery failed: " + response);
            }
        }
        
        private String sendCommand(String command) throws IOException {
            writer.println(command);
            String response = readResponse();
            if (!response.startsWith("250")) {
                throw new IOException("Command failed: " + command + " -> " + response);
            }
            return response;
        }
        
        private String readResponse() throws IOException {
            String line = reader.readLine();
            if (line == null) {
                throw new IOException("Connection closed by server");
            }
            return line;
        }
        
        @Override
        public void close() throws IOException {
            if (socket != null && !socket.isClosed()) {
                try {
                    writer.println("QUIT");
                    readResponse();
                } catch (IOException e) {
                    // 忽略QUIT错误
                }
                socket.close();
            }
        }
    }
}
