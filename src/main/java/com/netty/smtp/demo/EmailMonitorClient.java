package com.netty.smtp.demo;

import com.netty.smtp.service.LocalDeliveryService;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.file.*;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 邮件监控客户端 - 自动拉取和显示邮件信息
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class EmailMonitorClient {
    
    private static final String MAILBOX_DIR = "mailboxes";
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    
    private final LocalDeliveryService deliveryService;
    private final ScheduledExecutorService scheduler;
    private final Set<String> processedFiles;
    private final Map<String, Integer> mailboxStats;
    
    public EmailMonitorClient() {
        this.deliveryService = new LocalDeliveryService();
        this.scheduler = Executors.newScheduledThreadPool(2);
        this.processedFiles = new HashSet<>();
        this.mailboxStats = new HashMap<>();
        
        // 初始化时扫描已存在的邮件
        initializeProcessedFiles();
    }
    
    public static void main(String[] args) {
        System.out.println("=== 邮件监控客户端启动 ===");
        System.out.println("监控目录: " + MAILBOX_DIR);
        System.out.println("刷新间隔: 5秒");
        System.out.println("按 Ctrl+C 停止监控");
        System.out.println("================================");
        System.out.println();
        
        EmailMonitorClient monitor = new EmailMonitorClient();
        monitor.startMonitoring();
        
        // 添加关闭钩子
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            System.out.println("\n=== 邮件监控客户端停止 ===");
            monitor.shutdown();
        }));
        
        // 保持主线程运行
        try {
            Thread.currentThread().join();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
    
    /**
     * 开始监控邮件
     */
    public void startMonitoring() {
        // 每5秒检查新邮件
        scheduler.scheduleAtFixedRate(this::checkNewEmails, 0, 5, TimeUnit.SECONDS);
        
        // 每30秒显示统计信息
        scheduler.scheduleAtFixedRate(this::printStatistics, 10, 30, TimeUnit.SECONDS);
    }
    
    /**
     * 初始化已处理的文件列表
     */
    private void initializeProcessedFiles() {
        try {
            Path mailboxPath = Paths.get(MAILBOX_DIR);
            if (!Files.exists(mailboxPath)) {
                Files.createDirectories(mailboxPath);
                return;
            }
            
            Files.walk(mailboxPath)
                    .filter(path -> path.toString().endsWith(".eml"))
                    .forEach(path -> {
                        processedFiles.add(path.toString());
                        String mailbox = getMailboxFromPath(path);
                        mailboxStats.merge(mailbox, 1, Integer::sum);
                    });
                    
            if (!processedFiles.isEmpty()) {
                System.out.println("📂 发现 " + processedFiles.size() + " 封历史邮件");
                printMailboxSummary();
                System.out.println();
            }
            
        } catch (IOException e) {
            System.err.println("❌ 初始化邮件目录失败: " + e.getMessage());
        }
    }
    
    /**
     * 检查新邮件
     */
    private void checkNewEmails() {
        try {
            Path mailboxPath = Paths.get(MAILBOX_DIR);
            if (!Files.exists(mailboxPath)) {
                return;
            }
            
            List<Path> newEmails = new ArrayList<>();
            
            Files.walk(mailboxPath)
                    .filter(path -> path.toString().endsWith(".eml"))
                    .filter(path -> !processedFiles.contains(path.toString()))
                    .forEach(newEmails::add);
            
            for (Path emailPath : newEmails) {
                processNewEmail(emailPath);
                processedFiles.add(emailPath.toString());
                
                String mailbox = getMailboxFromPath(emailPath);
                mailboxStats.merge(mailbox, 1, Integer::sum);
            }
            
        } catch (IOException e) {
            System.err.println("❌ 检查新邮件失败: " + e.getMessage());
        }
    }
    
    /**
     * 处理新邮件
     */
    private void processNewEmail(Path emailPath) {
        try {
            String content = deliveryService.readMessage(emailPath);
            String mailbox = getMailboxFromPath(emailPath);
            String fileName = emailPath.getFileName().toString();
            
            System.out.println("📧 新邮件到达!");
            System.out.println("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
            System.out.println("📬 收件箱: " + mailbox);
            System.out.println("📄 文件名: " + fileName);
            System.out.println("📅 时间: " + DATE_FORMAT.format(new Date()));
            System.out.println("📍 路径: " + emailPath);
            System.out.println();
            
            // 解析并显示邮件头部信息
            parseAndDisplayEmailHeaders(content);
            
            // 显示邮件正文
            System.out.println("📝 邮件正文:");
            System.out.println("─────────────────────────────────────────────────");
            displayEmailBody(content);
            
            System.out.println("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
            System.out.println();
            
        } catch (IOException e) {
            System.err.println("❌ 读取邮件失败: " + emailPath + " - " + e.getMessage());
        }
    }
    
    /**
     * 解析并显示邮件头部信息
     */
    private void parseAndDisplayEmailHeaders(String content) {
        String[] lines = content.split("\r?\n");
        boolean inHeaders = true;
        Map<String, String> headers = new LinkedHashMap<>();
        String currentHeader = null;
        
        for (String line : lines) {
            if (inHeaders) {
                if (line.trim().isEmpty()) {
                    inHeaders = false;
                    break;
                }
                
                if (line.startsWith(" ") || line.startsWith("\t")) {
                    // 续行
                    if (currentHeader != null) {
                        headers.put(currentHeader, headers.get(currentHeader) + " " + line.trim());
                    }
                } else {
                    int colonIndex = line.indexOf(':');
                    if (colonIndex > 0) {
                        currentHeader = line.substring(0, colonIndex).trim();
                        String value = line.substring(colonIndex + 1).trim();
                        headers.put(currentHeader, value);
                    }
                }
            }
        }
        
        System.out.println("📋 邮件头部信息:");
        System.out.println("─────────────────────────────────────────────────");
        
        // 显示重要头部信息
        String[] importantHeaders = {"Delivered-To", "Return-Path", "From", "To", "Subject", "Date", "Message-ID"};
        
        for (String headerName : importantHeaders) {
            String value = headers.get(headerName);
            if (value != null) {
                System.out.printf("%-15s: %s%n", headerName, value);
            }
        }
        
        // 显示其他头部信息
        System.out.println();
        System.out.println("🔍 其他头部信息:");
        for (Map.Entry<String, String> entry : headers.entrySet()) {
            if (!Arrays.asList(importantHeaders).contains(entry.getKey())) {
                System.out.printf("%-15s: %s%n", entry.getKey(), entry.getValue());
            }
        }
        System.out.println();
    }
    
    /**
     * 显示邮件正文
     */
    private void displayEmailBody(String content) {
        String[] lines = content.split("\r?\n");
        boolean inBody = false;
        StringBuilder body = new StringBuilder();
        
        for (String line : lines) {
            if (inBody) {
                body.append(line).append("\n");
            } else if (line.trim().isEmpty()) {
                inBody = true;
            }
        }
        
        String bodyText = body.toString().trim();
        if (bodyText.isEmpty()) {
            System.out.println("(空邮件正文)");
        } else {
            System.out.println(bodyText);
        }
        System.out.println();
    }
    
    /**
     * 从路径获取邮箱名称
     */
    private String getMailboxFromPath(Path path) {
        Path parent = path.getParent();
        if (parent != null) {
            return parent.getFileName().toString();
        }
        return "unknown";
    }
    
    /**
     * 打印统计信息
     */
    private void printStatistics() {
        if (mailboxStats.isEmpty()) {
            return;
        }
        
        System.out.println("📊 邮箱统计信息 (" + DATE_FORMAT.format(new Date()) + ")");
        System.out.println("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
        printMailboxSummary();
        System.out.println("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
        System.out.println();
    }
    
    /**
     * 打印邮箱摘要
     */
    private void printMailboxSummary() {
        int totalEmails = mailboxStats.values().stream().mapToInt(Integer::intValue).sum();
        System.out.println("📈 总邮件数: " + totalEmails);
        System.out.println("📮 邮箱数量: " + mailboxStats.size());
        System.out.println();
        
        System.out.println("📬 各邮箱邮件数量:");
        mailboxStats.entrySet().stream()
                .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
                .forEach(entry -> 
                    System.out.printf("  %-30s: %3d 封%n", entry.getKey(), entry.getValue())
                );
    }
    
    /**
     * 关闭监控
     */
    public void shutdown() {
        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }
}
