package com.netty.smtp.protocol;

import com.netty.smtp.model.*;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * SMTP命令处理器
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Component
public class SmtpCommandProcessor {
    
    private static final Logger logger = LoggerFactory.getLogger(SmtpCommandProcessor.class);
    private static final Logger protocolLogger = LoggerFactory.getLogger("com.netty.smtp.protocol");
    
    // 邮件地址正则表达式
    private static final Pattern MAIL_FROM_PATTERN = Pattern.compile("^MAIL\\s+FROM:\\s*<(.*)>", Pattern.CASE_INSENSITIVE);
    private static final Pattern RCPT_TO_PATTERN = Pattern.compile("^RCPT\\s+TO:\\s*<(.*)>", Pattern.CASE_INSENSITIVE);
    

    
    // 配置参数
    private String serverHostname = "localhost";
    private int maxRecipients = 100;
    private long maxMessageSize = 25L * 1024 * 1024; // 25MB
    
    /**
     * 处理SMTP命令
     */
    public SmtpResponse processCommand(String commandLine, SmtpSession session) {
        if (commandLine == null || commandLine.trim().isEmpty()) {
            return SmtpResponse.Responses.SYNTAX_ERROR;
        }
        
        // 设置MDC用于日志追踪
        MDC.put("sessionId", session.getSessionId());
        
        try {
            session.updateActivity();
            
            String[] parts = commandLine.trim().split("\\s+", 2);
            String commandStr = parts[0].toUpperCase();
            String parameters = parts.length > 1 ? parts[1] : "";
            
            SmtpCommand command = SmtpCommand.fromString(commandStr);
            
            protocolLogger.debug("Received command: {} from {}", commandLine, session.getClientAddress());
            
            switch (command) {
                case HELO:
                    return handleHelo(parameters, session, false);
                case EHLO:
                    return handleHelo(parameters, session, true);
                case MAIL:
                    return handleMailFrom(commandLine, session);
                case RCPT:
                    return handleRcptTo(commandLine, session);
                case DATA:
                    return handleData(session);
                case RSET:
                    return handleReset(session);
                case NOOP:
                    return handleNoop(session);
                case QUIT:
                    return handleQuit(session);
                case VRFY:
                    return handleVerify(parameters, session);
                case HELP:
                    return handleHelp(session);
                default:
                    return handleUnknownCommand(commandStr, session);
            }
            
        } finally {
            MDC.remove("sessionId");
        }
    }
    
    /**
     * 处理HELO/EHLO命令
     */
    private SmtpResponse handleHelo(String hostname, SmtpSession session, boolean isExtended) {
        if (hostname == null || hostname.trim().isEmpty()) {
            return SmtpResponse.Responses.PARAMETER_ERROR;
        }
        
        session.setClientHostname(hostname.trim());
        session.setExtendedSmtp(isExtended);
        session.setState(SmtpSession.State.GREETED);
        
        protocolLogger.info("Client {} identified as: {}", session.getClientAddress(), hostname);
        
        if (isExtended) {
            // EHLO响应包含扩展功能列表
            String response = serverHostname + " Hello " + hostname + "\n" +
                    "SIZE " + maxMessageSize + "\n" +
                    "8BITMIME\n" +
                    "PIPELINING";
            return new SmtpResponse(250, response, true);
        } else {
            return new SmtpResponse(250, serverHostname + " Hello " + hostname);
        }
    }
    
    /**
     * 处理MAIL FROM命令
     */
    private SmtpResponse handleMailFrom(String commandLine, SmtpSession session) {
        if (session.getState() != SmtpSession.State.GREETED && 
            session.getState() != SmtpSession.State.COMPLETED) {
            return SmtpResponse.Responses.BAD_SEQUENCE;
        }
        
        Matcher matcher = MAIL_FROM_PATTERN.matcher(commandLine);
        if (!matcher.matches()) {
            return SmtpResponse.Responses.PARAMETER_ERROR;
        }
        
        String fromAddress = matcher.group(1).trim();
        
        // 验证邮件地址格式（允许空地址用于退信）
        if (!fromAddress.isEmpty() && !isValidEmailAddress(fromAddress)) {
            return SmtpResponse.Responses.invalidAddress(fromAddress);
        }
        
        session.setMailFrom(fromAddress);
        session.setState(SmtpSession.State.MAIL_STARTED);
        
        protocolLogger.info("Mail from: {}", fromAddress);
        
        return SmtpResponse.Responses.mailOk(fromAddress);
    }
    
    /**
     * 处理RCPT TO命令
     */
    private SmtpResponse handleRcptTo(String commandLine, SmtpSession session) {
        if (session.getState() != SmtpSession.State.MAIL_STARTED && 
            session.getState() != SmtpSession.State.RCPT_ADDED) {
            return SmtpResponse.Responses.BAD_SEQUENCE;
        }
        
        Matcher matcher = RCPT_TO_PATTERN.matcher(commandLine);
        if (!matcher.matches()) {
            return SmtpResponse.Responses.PARAMETER_ERROR;
        }
        
        String toAddress = matcher.group(1).trim();
        
        // 验证邮件地址格式
        if (!isValidEmailAddress(toAddress)) {
            return SmtpResponse.Responses.invalidAddress(toAddress);
        }
        
        // 检查收件人数量限制
        if (session.getRecipients().size() >= maxRecipients) {
            return SmtpResponse.Responses.tooManyRecipients(maxRecipients);
        }
        
        session.addRecipient(toAddress);
        session.setState(SmtpSession.State.RCPT_ADDED);
        
        protocolLogger.info("Recipient added: {}", toAddress);
        
        return SmtpResponse.Responses.rcptOk(toAddress);
    }
    
    /**
     * 处理DATA命令
     */
    private SmtpResponse handleData(SmtpSession session) {
        if (session.getState() != SmtpSession.State.RCPT_ADDED) {
            return SmtpResponse.Responses.BAD_SEQUENCE;
        }
        
        if (session.getRecipients().isEmpty()) {
            return new SmtpResponse(503, "No recipients specified");
        }
        
        session.setState(SmtpSession.State.DATA_STARTED);
        
        protocolLogger.info("Starting data transmission for {} recipients", 
                session.getRecipients().size());
        
        return SmtpResponse.Responses.START_MAIL_INPUT;
    }
    
    /**
     * 处理RSET命令
     */
    private SmtpResponse handleReset(SmtpSession session) {
        session.reset();
        
        protocolLogger.info("Session reset");
        
        return SmtpResponse.Responses.OK;
    }
    
    /**
     * 处理NOOP命令
     */
    private SmtpResponse handleNoop(SmtpSession session) {
        return SmtpResponse.Responses.OK;
    }
    
    /**
     * 处理QUIT命令
     */
    private SmtpResponse handleQuit(SmtpSession session) {
        session.setState(SmtpSession.State.QUIT);
        
        protocolLogger.info("Client disconnecting");
        
        return SmtpResponse.Responses.CLOSING_CHANNEL;
    }
    
    /**
     * 处理VRFY命令
     */
    private SmtpResponse handleVerify(String address, SmtpSession session) {
        // 出于安全考虑，不提供用户验证功能
        return new SmtpResponse(252, "Cannot VRFY user, but will accept message and attempt delivery");
    }
    
    /**
     * 处理HELP命令
     */
    private SmtpResponse handleHelp(SmtpSession session) {
        String helpText = "Commands supported:\n" +
                "HELO EHLO MAIL RCPT DATA RSET NOOP QUIT HELP";
        return new SmtpResponse(214, helpText, true);
    }
    
    /**
     * 处理未知命令
     */
    private SmtpResponse handleUnknownCommand(String command, SmtpSession session) {
        protocolLogger.warn("Unknown command received: {}", command);
        return SmtpResponse.Responses.SYNTAX_ERROR;
    }
    
    // Setters for configuration
    public void setServerHostname(String serverHostname) {
        this.serverHostname = serverHostname;
    }
    
    public void setMaxRecipients(int maxRecipients) {
        this.maxRecipients = maxRecipients;
    }
    
    public void setMaxMessageSize(long maxMessageSize) {
        this.maxMessageSize = maxMessageSize;
    }

    /**
     * 简单的邮件地址验证
     */
    private boolean isValidEmailAddress(String email) {
        if (email == null || email.trim().isEmpty()) {
            return false;
        }

        // 基本格式检查：必须包含@符号
        if (!email.contains("@")) {
            return false;
        }

        // 分割用户名和域名
        String[] parts = email.split("@");
        if (parts.length != 2) {
            return false;
        }

        String localPart = parts[0];
        String domain = parts[1];

        // 检查用户名部分
        if (localPart.isEmpty() || localPart.length() > 64) {
            return false;
        }

        // 检查域名部分
        if (domain.isEmpty() || domain.length() > 255) {
            return false;
        }

        // 允许localhost和IP地址用于测试
        if ("localhost".equals(domain) ||
            domain.matches("\\d+\\.\\d+\\.\\d+\\.\\d+") ||
            domain.matches("[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}")) {
            return true;
        }

        return false;
    }
}
