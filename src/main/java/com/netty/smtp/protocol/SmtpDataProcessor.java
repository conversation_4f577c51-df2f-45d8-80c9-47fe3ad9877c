package com.netty.smtp.protocol;

import com.netty.smtp.model.EmailMessage;
import com.netty.smtp.model.SmtpResponse;
import com.netty.smtp.model.SmtpSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;

/**
 * SMTP数据处理器 - 处理邮件内容传输
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Component
public class SmtpDataProcessor {
    
    private static final Logger logger = LoggerFactory.getLogger(SmtpDataProcessor.class);
    private static final Logger protocolLogger = LoggerFactory.getLogger("com.netty.smtp.protocol");

    // 邮件结束标记
    private static final String DATA_END_MARKER = ".";
    private static final String CRLF = "\r\n";
    private static final String SESSION_ID_KEY = "sessionId";

    private long maxMessageSize = 25L * 1024 * 1024; // 25MB
    
    /**
     * 处理邮件数据行
     * 
     * @param line 数据行
     * @param session SMTP会话
     * @return 处理结果，如果返回非null则表示数据传输结束
     */
    public SmtpResponse processDataLine(String line, SmtpSession session) {
        MDC.put(SESSION_ID_KEY, session.getSessionId());
        
        try {
            // 检查会话状态
            if (session.getState() != SmtpSession.State.DATA_STARTED && 
                session.getState() != SmtpSession.State.DATA_RECEIVING) {
                return new SmtpResponse(503, "Bad sequence of commands");
            }
            
            // 设置为数据接收状态
            if (session.getState() == SmtpSession.State.DATA_STARTED) {
                session.setState(SmtpSession.State.DATA_RECEIVING);
            }
            
            // 检查是否为结束标记
            if (DATA_END_MARKER.equals(line.trim())) {
                return completeDataTransmission(session);
            }
            
            // 处理点号转义（RFC 5321）
            String processedLine = line;
            if (line.startsWith("..")) {
                processedLine = line.substring(1);
            }
            
            // 添加CRLF并检查大小限制
            String dataToAdd = processedLine + CRLF;
            
            if (session.getMessageSize() + dataToAdd.length() > maxMessageSize) {
                protocolLogger.warn("Message size limit exceeded: {} bytes", 
                        session.getMessageSize() + dataToAdd.length());
                return SmtpResponse.Responses.messageTooLarge(maxMessageSize);
            }
            
            // 添加数据到会话
            session.appendMessageData(dataToAdd);
            
            // 继续接收数据
            return null;
            
        } finally {
            MDC.remove(SESSION_ID_KEY);
        }
    }
    
    /**
     * 完成数据传输
     */
    private SmtpResponse completeDataTransmission(SmtpSession session) {
        try {
            // 创建邮件消息对象
            EmailMessage message = new EmailMessage(
                    session.getSessionId(),
                    session.getMailFrom(),
                    session.getRecipients(),
                    session.getCompleteMessage()
            );
            
            protocolLogger.info("Message received: ID={}, From={}, Recipients={}, Size={} bytes",
                    message.getMessageId(),
                    message.getFrom(),
                    message.getRecipients().size(),
                    message.getSize());
            
            // 这里可以添加邮件处理逻辑，比如：
            // 1. 保存到数据库
            // 2. 转发到其他服务器
            // 3. 本地投递
            // 目前先标记为已处理
            message.markAsProcessed();
            
            // 重置会话状态
            session.setState(SmtpSession.State.COMPLETED);
            session.clearMessageData();
            
            return new SmtpResponse(250, "OK: Message accepted for delivery, ID=" + message.getMessageId());
            
        } catch (Exception e) {
            logger.error("Error processing message data", e);
            return new SmtpResponse(554, "Transaction failed: " + e.getMessage());
        }
    }
    

    
    /**
     * 处理数据传输中断
     */
    public SmtpResponse handleDataInterruption(SmtpSession session, String reason) {
        MDC.put(SESSION_ID_KEY, session.getSessionId());

        try {
            protocolLogger.warn("Data transmission interrupted: {}", reason);

            // 清理会话数据
            session.clearMessageData();
            session.setState(SmtpSession.State.GREETED);

            return new SmtpResponse(554, "Transaction failed: " + reason);

        } finally {
            MDC.remove(SESSION_ID_KEY);
        }
    }
    
    /**
     * 获取当前数据传输状态信息
     */
    public String getDataTransmissionStatus(SmtpSession session) {
        return String.format("Session: %s, State: %s, Data size: %d bytes, Recipients: %d",
                session.getSessionId(),
                session.getState(),
                session.getMessageSize(),
                session.getRecipients().size());
    }
    
    // Configuration setters
    public void setMaxMessageSize(long maxMessageSize) {
        this.maxMessageSize = maxMessageSize;
    }
    
    public long getMaxMessageSize() {
        return maxMessageSize;
    }
}
