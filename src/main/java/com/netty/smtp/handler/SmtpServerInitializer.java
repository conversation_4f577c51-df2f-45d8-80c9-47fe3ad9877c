package com.netty.smtp.handler;

import com.netty.smtp.protocol.SmtpCommandProcessor;
import com.netty.smtp.protocol.SmtpDataProcessor;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.ChannelPipeline;
import io.netty.channel.socket.SocketChannel;
import io.netty.handler.codec.DelimiterBasedFrameDecoder;
import io.netty.handler.codec.Delimiters;
import io.netty.handler.codec.string.StringDecoder;
import io.netty.handler.codec.string.StringEncoder;
import io.netty.handler.timeout.IdleStateHandler;
import io.netty.util.CharsetUtil;

import java.util.concurrent.TimeUnit;

/**
 * SMTP服务器通道初始化器
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class SmtpServerInitializer extends ChannelInitializer<SocketChannel> {
    
    private final SmtpCommandProcessor commandProcessor;
    private final SmtpDataProcessor dataProcessor;
    private final String serverHostname;
    private final int connectionTimeout;
    
    public SmtpServerInitializer(SmtpCommandProcessor commandProcessor,
                               SmtpDataProcessor dataProcessor,
                               String serverHostname,
                               int connectionTimeout) {
        this.commandProcessor = commandProcessor;
        this.dataProcessor = dataProcessor;
        this.serverHostname = serverHostname;
        this.connectionTimeout = connectionTimeout;
    }
    
    @Override
    protected void initChannel(SocketChannel ch) throws Exception {
        ChannelPipeline pipeline = ch.pipeline();
        
        // 添加空闲状态处理器（用于连接超时）
        pipeline.addLast("idleStateHandler", 
                new IdleStateHandler(connectionTimeout, 0, 0, TimeUnit.SECONDS));
        
        // 添加基于分隔符的帧解码器（SMTP使用CRLF作为行分隔符）
        pipeline.addLast("frameDecoder", 
                new DelimiterBasedFrameDecoder(8192, Delimiters.lineDelimiter()));
        
        // 添加字符串解码器
        pipeline.addLast("stringDecoder", new StringDecoder(CharsetUtil.UTF_8));
        
        // 添加字符串编码器
        pipeline.addLast("stringEncoder", new StringEncoder(CharsetUtil.UTF_8));
        
        // 添加超时处理器
        pipeline.addLast("timeoutHandler", new SmtpTimeoutHandler());
        
        // 添加SMTP协议处理器
        pipeline.addLast("smtpHandler", 
                new SmtpServerHandler(commandProcessor, dataProcessor, serverHostname));
    }
}
