package com.netty.smtp.handler;

import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import io.netty.handler.timeout.IdleState;
import io.netty.handler.timeout.IdleStateEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * SMTP连接超时处理器
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class SmtpTimeoutHandler extends ChannelInboundHandlerAdapter {
    
    private static final Logger logger = LoggerFactory.getLogger(SmtpTimeoutHandler.class);
    
    @Override
    public void userEventTriggered(ChannelHandlerContext ctx, Object evt) throws Exception {
        if (evt instanceof IdleStateEvent) {
            IdleStateEvent event = (IdleStateEvent) evt;
            
            if (event.state() == IdleState.READER_IDLE) {
                logger.warn("Connection idle timeout: {}", ctx.channel().remoteAddress());
                
                // 获取SMTP处理器并调用超时处理方法
                SmtpServerHandler smtpHandler = ctx.pipeline().get(SmtpServerHandler.class);
                if (smtpHandler != null) {
                    smtpHandler.handleTimeout(ctx);
                } else {
                    ctx.close();
                }
            }
        } else {
            super.userEventTriggered(ctx, evt);
        }
    }
}
