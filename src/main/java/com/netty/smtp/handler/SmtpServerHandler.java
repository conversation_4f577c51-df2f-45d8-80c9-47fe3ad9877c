package com.netty.smtp.handler;

import com.netty.smtp.model.SmtpResponse;
import com.netty.smtp.model.SmtpSession;
import com.netty.smtp.protocol.SmtpCommandProcessor;
import com.netty.smtp.protocol.SmtpDataProcessor;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.SimpleChannelInboundHandler;
import io.netty.util.AttributeKey;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

/**
 * SMTP服务器处理器 - 处理客户端连接和SMTP协议交互
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class SmtpServerHandler extends SimpleChannelInboundHandler<String> {
    
    private static final Logger logger = LoggerFactory.getLogger(SmtpServerHandler.class);
    private static final Logger protocolLogger = LoggerFactory.getLogger("com.netty.smtp.protocol");
    
    // 会话属性键
    private static final AttributeKey<SmtpSession> SESSION_KEY = AttributeKey.valueOf("smtp.session");
    
    private final SmtpCommandProcessor commandProcessor;
    private final SmtpDataProcessor dataProcessor;
    private final String serverHostname;
    
    public SmtpServerHandler(SmtpCommandProcessor commandProcessor, 
                           SmtpDataProcessor dataProcessor,
                           String serverHostname) {
        this.commandProcessor = commandProcessor;
        this.dataProcessor = dataProcessor;
        this.serverHostname = serverHostname;
    }
    
    @Override
    public void channelActive(ChannelHandlerContext ctx) throws Exception {
        // 获取客户端地址
        String clientAddress = ctx.channel().remoteAddress().toString();
        
        // 创建SMTP会话
        SmtpSession session = new SmtpSession(clientAddress);
        ctx.channel().attr(SESSION_KEY).set(session);
        
        // 设置MDC用于日志追踪
        MDC.put("sessionId", session.getSessionId());
        
        try {
            logger.info("New SMTP connection from: {}", clientAddress);
            protocolLogger.info("Connection established from: {}", clientAddress);
            
            // 发送欢迎消息
            SmtpResponse welcomeResponse = SmtpResponse.Responses.serviceReady(serverHostname);
            sendResponse(ctx, welcomeResponse);
            
        } finally {
            MDC.remove("sessionId");
        }
        
        super.channelActive(ctx);
    }
    
    @Override
    public void channelInactive(ChannelHandlerContext ctx) throws Exception {
        SmtpSession session = ctx.channel().attr(SESSION_KEY).get();
        
        if (session != null) {
            MDC.put("sessionId", session.getSessionId());
            
            try {
                logger.info("SMTP connection closed: {}", session.getClientAddress());
                protocolLogger.info("Connection closed from: {}", session.getClientAddress());
                
            } finally {
                MDC.remove("sessionId");
            }
        }
        
        super.channelInactive(ctx);
    }
    
    @Override
    protected void channelRead0(ChannelHandlerContext ctx, String message) throws Exception {
        SmtpSession session = ctx.channel().attr(SESSION_KEY).get();
        
        if (session == null) {
            logger.error("No session found for connection");
            ctx.close();
            return;
        }
        
        MDC.put("sessionId", session.getSessionId());
        
        try {
            protocolLogger.debug("Received: {}", message);
            
            SmtpResponse response;
            
            // 检查是否在数据传输模式
            if (session.getState() == SmtpSession.State.DATA_STARTED || 
                session.getState() == SmtpSession.State.DATA_RECEIVING) {
                
                // 处理邮件数据
                response = dataProcessor.processDataLine(message, session);
                
                // 如果返回null，表示继续接收数据，不发送响应
                if (response == null) {
                    return;
                }
                
            } else {
                // 处理SMTP命令
                response = commandProcessor.processCommand(message, session);
            }
            
            // 发送响应
            sendResponse(ctx, response);
            
            // 如果是QUIT命令，关闭连接
            if (session.getState() == SmtpSession.State.QUIT) {
                ctx.close();
            }
            
        } catch (Exception e) {
            logger.error("Error processing SMTP message", e);
            SmtpResponse errorResponse = new SmtpResponse(554, "Transaction failed: " + e.getMessage());
            sendResponse(ctx, errorResponse);
            
        } finally {
            MDC.remove("sessionId");
        }
    }
    
    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
        SmtpSession session = ctx.channel().attr(SESSION_KEY).get();
        
        if (session != null) {
            MDC.put("sessionId", session.getSessionId());
        }
        
        try {
            logger.error("Exception in SMTP handler", cause);
            
            // 发送错误响应
            SmtpResponse errorResponse = new SmtpResponse(421, "Service not available, closing transmission channel");
            sendResponse(ctx, errorResponse);
            
            // 关闭连接
            ctx.close();
            
        } finally {
            if (session != null) {
                MDC.remove("sessionId");
            }
        }
    }
    
    /**
     * 发送SMTP响应
     */
    private void sendResponse(ChannelHandlerContext ctx, SmtpResponse response) {
        if (response == null) {
            return;
        }
        
        String responseText = response.toSmtpString() + "\r\n";
        
        protocolLogger.debug("Sending: {}", response.toSmtpString());
        
        ctx.writeAndFlush(responseText);
    }
    
    /**
     * 处理连接超时
     */
    public void handleTimeout(ChannelHandlerContext ctx) {
        SmtpSession session = ctx.channel().attr(SESSION_KEY).get();
        
        if (session != null) {
            MDC.put("sessionId", session.getSessionId());
            
            try {
                logger.warn("Connection timeout for session: {}", session.getSessionId());
                
                SmtpResponse timeoutResponse = new SmtpResponse(421, "Timeout. Closing connection.");
                sendResponse(ctx, timeoutResponse);
                
            } finally {
                MDC.remove("sessionId");
            }
        }
        
        ctx.close();
    }
}
