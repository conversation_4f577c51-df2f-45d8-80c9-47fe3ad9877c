package com.netty.smtp.server;

import com.netty.smtp.handler.SmtpServerInitializer;
import com.netty.smtp.protocol.SmtpCommandProcessor;
import com.netty.smtp.protocol.SmtpDataProcessor;
import io.netty.bootstrap.ServerBootstrap;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelOption;
import io.netty.channel.EventLoopGroup;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Netty SMTP服务器
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Component
public class NettySmtpServer {
    
    private static final Logger logger = LoggerFactory.getLogger(NettySmtpServer.class);
    
    @Autowired
    private SmtpCommandProcessor commandProcessor;
    
    @Autowired
    private SmtpDataProcessor dataProcessor;
    
    private EventLoopGroup bossGroup;
    private EventLoopGroup workerGroup;
    private ChannelFuture channelFuture;
    private final AtomicBoolean isRunning = new AtomicBoolean(false);
    
    // 配置参数
    private String hostname = "localhost";
    private int port = 2525;
    private int maxConnections = 100;
    private int connectionTimeout = 300; // 5分钟
    private int bossThreads = 1;
    private int workerThreads = 0; // 0表示使用默认值
    
    /**
     * 启动SMTP服务器
     */
    public void start() throws InterruptedException {
        if (isRunning.get()) {
            logger.warn("SMTP server is already running");
            return;
        }
        
        logger.info("Starting SMTP server on {}:{}", hostname, port);
        
        // 创建事件循环组
        bossGroup = new NioEventLoopGroup(bossThreads);
        workerGroup = new NioEventLoopGroup(workerThreads);
        
        try {
            ServerBootstrap bootstrap = new ServerBootstrap();
            bootstrap.group(bossGroup, workerGroup)
                    .channel(NioServerSocketChannel.class)
                    .childHandler(new SmtpServerInitializer(
                            commandProcessor, 
                            dataProcessor, 
                            hostname, 
                            connectionTimeout))
                    .option(ChannelOption.SO_BACKLOG, maxConnections)
                    .option(ChannelOption.SO_REUSEADDR, true)
                    .childOption(ChannelOption.SO_KEEPALIVE, true)
                    .childOption(ChannelOption.TCP_NODELAY, true);
            
            // 绑定端口并启动服务器
            channelFuture = bootstrap.bind(port).sync();
            
            if (channelFuture.isSuccess()) {
                isRunning.set(true);
                logger.info("SMTP server started successfully on port {}", port);
                
                // 配置处理器参数
                commandProcessor.setServerHostname(hostname);
                commandProcessor.setMaxRecipients(100);
                commandProcessor.setMaxMessageSize(25 * 1024 * 1024);
                
                dataProcessor.setMaxMessageSize(25 * 1024 * 1024);
                
            } else {
                logger.error("Failed to start SMTP server on port {}", port);
                throw new RuntimeException("Failed to start SMTP server");
            }
            
        } catch (Exception e) {
            logger.error("Error starting SMTP server", e);
            shutdown();
            throw e;
        }
    }
    
    /**
     * 停止SMTP服务器
     */
    @PreDestroy
    public void shutdown() {
        if (!isRunning.get()) {
            return;
        }
        
        logger.info("Shutting down SMTP server...");
        
        try {
            // 关闭服务器通道
            if (channelFuture != null) {
                channelFuture.channel().close().sync();
            }
            
        } catch (InterruptedException e) {
            logger.warn("Interrupted while closing server channel", e);
            Thread.currentThread().interrupt();
            
        } finally {
            // 关闭事件循环组
            if (workerGroup != null) {
                workerGroup.shutdownGracefully();
            }
            if (bossGroup != null) {
                bossGroup.shutdownGracefully();
            }
            
            isRunning.set(false);
            logger.info("SMTP server shutdown completed");
        }
    }
    
    /**
     * 等待服务器关闭
     */
    public void waitForShutdown() throws InterruptedException {
        if (channelFuture != null) {
            channelFuture.channel().closeFuture().sync();
        }
    }
    
    /**
     * 检查服务器是否正在运行
     */
    public boolean isRunning() {
        return isRunning.get();
    }
    
    /**
     * 获取服务器状态信息
     */
    public String getServerStatus() {
        return String.format("SMTP Server - Running: %s, Host: %s, Port: %d, Max Connections: %d",
                isRunning.get(), hostname, port, maxConnections);
    }
    
    // Configuration setters
    public void setHostname(String hostname) {
        this.hostname = hostname;
    }
    
    public void setPort(int port) {
        this.port = port;
    }
    
    public void setMaxConnections(int maxConnections) {
        this.maxConnections = maxConnections;
    }
    
    public void setConnectionTimeout(int connectionTimeout) {
        this.connectionTimeout = connectionTimeout;
    }
    
    public void setBossThreads(int bossThreads) {
        this.bossThreads = bossThreads;
    }
    
    public void setWorkerThreads(int workerThreads) {
        this.workerThreads = workerThreads;
    }
    
    // Getters
    public String getHostname() {
        return hostname;
    }
    
    public int getPort() {
        return port;
    }
    
    public int getMaxConnections() {
        return maxConnections;
    }
    
    public int getConnectionTimeout() {
        return connectionTimeout;
    }
}
