package com.netty.smtp.config;

import com.netty.smtp.server.NettySmtpServer;
import com.netty.smtp.service.SmtpServerService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.EventListener;

/**
 * SMTP服务器配置类
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Configuration
public class SmtpServerConfiguration {
    
    private static final Logger logger = LoggerFactory.getLogger(SmtpServerConfiguration.class);
    
    @Autowired
    private SmtpServerProperties properties;
    
    @Autowired
    private NettySmtpServer smtpServer;
    
    @Autowired
    private SmtpServerService smtpServerService;
    
    /**
     * 应用启动完成后自动启动SMTP服务器
     */
    @EventListener(ApplicationReadyEvent.class)
    public void startSmtpServer() {
        try {
            logger.info("Configuring SMTP server with properties: {}", getConfigSummary());
            
            // 配置服务器参数
            smtpServer.setHostname(properties.getServer().getHostname());
            smtpServer.setPort(properties.getServer().getPort());
            smtpServer.setMaxConnections(properties.getServer().getMaxConnections());
            smtpServer.setConnectionTimeout(properties.getServer().getConnectionTimeout());
            
            // 启动服务器
            smtpServerService.startServer();
            
            logger.info("SMTP server configuration completed successfully");
            
        } catch (Exception e) {
            logger.error("Failed to start SMTP server", e);
            throw new RuntimeException("SMTP server startup failed", e);
        }
    }
    
    /**
     * 获取配置摘要信息
     */
    private String getConfigSummary() {
        return String.format("Host=%s, Port=%d, MaxConn=%d, Timeout=%ds, MaxSize=%dMB, MaxRecipients=%d",
                properties.getServer().getHostname(),
                properties.getServer().getPort(),
                properties.getServer().getMaxConnections(),
                properties.getServer().getConnectionTimeout(),
                properties.getMail().getMaxSize(),
                properties.getMail().getMaxRecipients());
    }
}
