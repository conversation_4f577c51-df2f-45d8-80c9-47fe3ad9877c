package com.netty.smtp.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * SMTP服务器配置属性
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Component
@ConfigurationProperties(prefix = "smtp")
public class SmtpServerProperties {
    
    private Server server = new Server();
    private Mail mail = new Mail();
    private Relay relay = new Relay();
    private Domains domains = new Domains();
    
    public static class Server {
        private int port = 2525;
        private String hostname = "localhost";
        private int maxConnections = 100;
        private int connectionTimeout = 300;
        private boolean sslEnabled = false;
        private int sslPort = 2465;
        private boolean starttlsEnabled = true;
        
        // Getters and Setters
        public int getPort() {
            return port;
        }
        
        public void setPort(int port) {
            this.port = port;
        }
        
        public String getHostname() {
            return hostname;
        }
        
        public void setHostname(String hostname) {
            this.hostname = hostname;
        }
        
        public int getMaxConnections() {
            return maxConnections;
        }
        
        public void setMaxConnections(int maxConnections) {
            this.maxConnections = maxConnections;
        }
        
        public int getConnectionTimeout() {
            return connectionTimeout;
        }
        
        public void setConnectionTimeout(int connectionTimeout) {
            this.connectionTimeout = connectionTimeout;
        }
        
        public boolean isSslEnabled() {
            return sslEnabled;
        }
        
        public void setSslEnabled(boolean sslEnabled) {
            this.sslEnabled = sslEnabled;
        }
        
        public int getSslPort() {
            return sslPort;
        }
        
        public void setSslPort(int sslPort) {
            this.sslPort = sslPort;
        }
        
        public boolean isStarttlsEnabled() {
            return starttlsEnabled;
        }
        
        public void setStarttlsEnabled(boolean starttlsEnabled) {
            this.starttlsEnabled = starttlsEnabled;
        }
    }
    
    public static class Mail {
        private int maxSize = 25; // MB
        private int maxRecipients = 100;
        private int queueSize = 1000;
        private int workerThreads = 10;
        
        // Getters and Setters
        public int getMaxSize() {
            return maxSize;
        }
        
        public void setMaxSize(int maxSize) {
            this.maxSize = maxSize;
        }
        
        public int getMaxRecipients() {
            return maxRecipients;
        }
        
        public void setMaxRecipients(int maxRecipients) {
            this.maxRecipients = maxRecipients;
        }
        
        public int getQueueSize() {
            return queueSize;
        }
        
        public void setQueueSize(int queueSize) {
            this.queueSize = queueSize;
        }
        
        public int getWorkerThreads() {
            return workerThreads;
        }
        
        public void setWorkerThreads(int workerThreads) {
            this.workerThreads = workerThreads;
        }
    }
    
    public static class Relay {
        private boolean enabled = true;
        private String defaultHost = "smtp.gmail.com";
        private int defaultPort = 587;
        private Auth auth = new Auth();
        private Retry retry = new Retry();
        
        public static class Auth {
            private String username = "";
            private String password = "";
            
            public String getUsername() {
                return username;
            }
            
            public void setUsername(String username) {
                this.username = username;
            }
            
            public String getPassword() {
                return password;
            }
            
            public void setPassword(String password) {
                this.password = password;
            }
        }
        
        public static class Retry {
            private int maxAttempts = 3;
            private int delaySeconds = 5;
            
            public int getMaxAttempts() {
                return maxAttempts;
            }
            
            public void setMaxAttempts(int maxAttempts) {
                this.maxAttempts = maxAttempts;
            }
            
            public int getDelaySeconds() {
                return delaySeconds;
            }
            
            public void setDelaySeconds(int delaySeconds) {
                this.delaySeconds = delaySeconds;
            }
        }
        
        // Getters and Setters
        public boolean isEnabled() {
            return enabled;
        }
        
        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }
        
        public String getDefaultHost() {
            return defaultHost;
        }
        
        public void setDefaultHost(String defaultHost) {
            this.defaultHost = defaultHost;
        }
        
        public int getDefaultPort() {
            return defaultPort;
        }
        
        public void setDefaultPort(int defaultPort) {
            this.defaultPort = defaultPort;
        }
        
        public Auth getAuth() {
            return auth;
        }
        
        public void setAuth(Auth auth) {
            this.auth = auth;
        }
        
        public Retry getRetry() {
            return retry;
        }
        
        public void setRetry(Retry retry) {
            this.retry = retry;
        }
    }
    
    public static class Domains {
        private List<String> local = Arrays.asList("localhost", "127.0.0.1", "local.test");
        private Map<String, String> routing = new HashMap<>();
        
        public List<String> getLocal() {
            return local;
        }
        
        public void setLocal(List<String> local) {
            this.local = local;
        }
        
        public Map<String, String> getRouting() {
            return routing;
        }
        
        public void setRouting(Map<String, String> routing) {
            this.routing = routing;
        }
    }
    
    // Main getters and setters
    public Server getServer() {
        return server;
    }
    
    public void setServer(Server server) {
        this.server = server;
    }
    
    public Mail getMail() {
        return mail;
    }
    
    public void setMail(Mail mail) {
        this.mail = mail;
    }
    
    public Relay getRelay() {
        return relay;
    }
    
    public void setRelay(Relay relay) {
        this.relay = relay;
    }
    
    public Domains getDomains() {
        return domains;
    }
    
    public void setDomains(Domains domains) {
        this.domains = domains;
    }
}
