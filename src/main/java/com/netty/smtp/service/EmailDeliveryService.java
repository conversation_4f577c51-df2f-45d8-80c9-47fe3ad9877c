package com.netty.smtp.service;

import com.netty.smtp.config.SmtpServerProperties;
import com.netty.smtp.model.EmailMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 邮件投递服务
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Service
public class EmailDeliveryService {
    
    private static final Logger logger = LoggerFactory.getLogger(EmailDeliveryService.class);
    private static final Logger deliveryLogger = LoggerFactory.getLogger("com.netty.smtp.service.delivery");
    
    @Autowired
    private SmtpServerProperties properties;
    
    @Autowired
    private EmailRelayService relayService;
    
    @Autowired
    private LocalDeliveryService localDeliveryService;
    
    private final ExecutorService deliveryExecutor;
    
    public EmailDeliveryService() {
        this.deliveryExecutor = Executors.newFixedThreadPool(10);
    }
    
    /**
     * 处理邮件投递
     */
    public CompletableFuture<DeliveryResult> deliverEmail(EmailMessage message) {
        return CompletableFuture.supplyAsync(() -> {
            MDC.put("messageId", message.getMessageId());
            
            try {
                deliveryLogger.info("Starting delivery for message: from={}, recipients={}, size={}",
                        message.getFrom(), message.getRecipients().size(), message.getSize());
                
                DeliveryResult result = new DeliveryResult(message.getMessageId());
                
                // 按域名分组收件人
                Map<String, List<String>> recipientsByDomain = groupRecipientsByDomain(message.getRecipients());

                for (Map.Entry<String, List<String>> entry : recipientsByDomain.entrySet()) {
                    String domain = entry.getKey();
                    List<String> recipients = entry.getValue();
                    
                    try {
                        if (isLocalDomain(domain)) {
                            // 本地投递
                            deliverLocally(message, recipients, result);
                        } else {
                            // 远程转发
                            deliverRemotely(message, domain, recipients, result);
                        }
                    } catch (Exception e) {
                        deliveryLogger.error("Failed to deliver to domain {}: {}", domain, e.getMessage());
                        result.addFailure(domain, recipients, e.getMessage());
                    }
                }
                
                // 标记消息处理完成
                if (result.hasFailures()) {
                    message.markAsFailed(result.getFailureSummary());
                } else {
                    message.markAsProcessed();
                }
                
                deliveryLogger.info("Delivery completed: successes={}, failures={}",
                        result.getSuccessCount(), result.getFailureCount());
                
                return result;
                
            } catch (Exception e) {
                deliveryLogger.error("Delivery failed for message {}", message.getMessageId(), e);
                message.markAsFailed(e.getMessage());
                
                DeliveryResult result = new DeliveryResult(message.getMessageId());
                result.addFailure("all", message.getRecipients(), e.getMessage());
                return result;
                
            } finally {
                MDC.remove("messageId");
            }
        }, deliveryExecutor);
    }
    
    /**
     * 本地投递
     */
    private void deliverLocally(EmailMessage message, List<String> recipients, DeliveryResult result) {
        deliveryLogger.debug("Delivering locally to {} recipients", recipients.size());
        
        try {
            localDeliveryService.deliverMessage(message, recipients);
            result.addSuccess("local", recipients);
            
            deliveryLogger.info("Local delivery successful for {} recipients", recipients.size());
            
        } catch (Exception e) {
            deliveryLogger.error("Local delivery failed: {}", e.getMessage());
            result.addFailure("local", recipients, e.getMessage());
        }
    }
    
    /**
     * 远程转发
     */
    private void deliverRemotely(EmailMessage message, String domain, List<String> recipients, DeliveryResult result) {
        deliveryLogger.debug("Delivering remotely to domain {} for {} recipients", domain, recipients.size());
        
        try {
            relayService.relayMessage(message, domain, recipients);
            result.addSuccess(domain, recipients);
            
            deliveryLogger.info("Remote delivery successful to domain {} for {} recipients", domain, recipients.size());
            
        } catch (Exception e) {
            deliveryLogger.error("Remote delivery failed to domain {}: {}", domain, e.getMessage());
            result.addFailure(domain, recipients, e.getMessage());
        }
    }
    
    /**
     * 按域名分组收件人
     */
    private java.util.Map<String, List<String>> groupRecipientsByDomain(List<String> recipients) {
        return recipients.stream()
                .collect(java.util.stream.Collectors.groupingBy(this::extractDomain));
    }
    
    /**
     * 从邮件地址提取域名
     */
    private String extractDomain(String email) {
        int atIndex = email.lastIndexOf('@');
        if (atIndex > 0 && atIndex < email.length() - 1) {
            return email.substring(atIndex + 1).toLowerCase();
        }
        return "unknown";
    }
    
    /**
     * 检查是否为本地域名
     */
    private boolean isLocalDomain(String domain) {
        return properties.getDomains().getLocal().contains(domain.toLowerCase());
    }
    
    /**
     * 投递结果类
     */
    public static class DeliveryResult {
        private final String messageId;
        private final java.util.Map<String, List<String>> successes = new java.util.HashMap<>();
        private final java.util.Map<String, DeliveryFailure> failures = new java.util.HashMap<>();
        
        public DeliveryResult(String messageId) {
            this.messageId = messageId;
        }
        
        public void addSuccess(String domain, List<String> recipients) {
            successes.put(domain, recipients);
        }
        
        public void addFailure(String domain, List<String> recipients, String error) {
            failures.put(domain, new DeliveryFailure(recipients, error));
        }
        
        public boolean hasFailures() {
            return !failures.isEmpty();
        }
        
        public int getSuccessCount() {
            return successes.values().stream().mapToInt(List::size).sum();
        }
        
        public int getFailureCount() {
            return failures.values().stream().mapToInt(f -> f.recipients.size()).sum();
        }
        
        public String getFailureSummary() {
            if (failures.isEmpty()) {
                return null;
            }
            
            StringBuilder sb = new StringBuilder();
            for (Map.Entry<String, DeliveryFailure> entry : failures.entrySet()) {
                sb.append(entry.getKey()).append(": ").append(entry.getValue().error).append("; ");
            }
            return sb.toString();
        }
        
        public String getMessageId() {
            return messageId;
        }
        
        public java.util.Map<String, List<String>> getSuccesses() {
            return successes;
        }
        
        public java.util.Map<String, DeliveryFailure> getFailures() {
            return failures;
        }
        
        public static class DeliveryFailure {
            private final List<String> recipients;
            private final String error;
            
            public DeliveryFailure(List<String> recipients, String error) {
                this.recipients = recipients;
                this.error = error;
            }
            
            public List<String> getRecipients() {
                return recipients;
            }
            
            public String getError() {
                return error;
            }
        }
    }
}
