package com.netty.smtp.service;

import com.netty.smtp.config.SmtpServerProperties;
import com.netty.smtp.server.NettySmtpServer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * SMTP服务器管理服务
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Service
public class SmtpServerService {
    
    private static final Logger logger = LoggerFactory.getLogger(SmtpServerService.class);
    
    @Autowired
    private NettySmtpServer smtpServer;
    
    @Autowired
    private SmtpServerProperties properties;
    
    private final AtomicBoolean isStarting = new AtomicBoolean(false);
    private final AtomicBoolean isStopping = new AtomicBoolean(false);
    
    /**
     * 启动SMTP服务器
     */
    public CompletableFuture<Void> startServer() {
        if (smtpServer.isRunning()) {
            logger.warn("SMTP server is already running");
            return CompletableFuture.completedFuture(null);
        }
        
        if (!isStarting.compareAndSet(false, true)) {
            logger.warn("SMTP server is already starting");
            return CompletableFuture.completedFuture(null);
        }
        
        return CompletableFuture.runAsync(() -> {
            try {
                logger.info("Starting SMTP server...");
                
                // 应用配置
                applyConfiguration();
                
                // 启动服务器
                smtpServer.start();
                
                logger.info("SMTP server started successfully on {}:{}", 
                        properties.getServer().getHostname(), 
                        properties.getServer().getPort());
                
            } catch (Exception e) {
                logger.error("Failed to start SMTP server", e);
                throw new RuntimeException("SMTP server startup failed", e);
                
            } finally {
                isStarting.set(false);
            }
        });
    }
    
    /**
     * 停止SMTP服务器
     */
    public CompletableFuture<Void> stopServer() {
        if (!smtpServer.isRunning()) {
            logger.warn("SMTP server is not running");
            return CompletableFuture.completedFuture(null);
        }
        
        if (!isStopping.compareAndSet(false, true)) {
            logger.warn("SMTP server is already stopping");
            return CompletableFuture.completedFuture(null);
        }
        
        return CompletableFuture.runAsync(() -> {
            try {
                logger.info("Stopping SMTP server...");
                
                smtpServer.shutdown();
                
                logger.info("SMTP server stopped successfully");
                
            } catch (Exception e) {
                logger.error("Error stopping SMTP server", e);
                throw new RuntimeException("SMTP server shutdown failed", e);
                
            } finally {
                isStopping.set(false);
            }
        });
    }
    
    /**
     * 重启SMTP服务器
     */
    public CompletableFuture<Void> restartServer() {
        return stopServer().thenCompose(v -> startServer());
    }
    
    /**
     * 获取服务器状态
     */
    public ServerStatus getServerStatus() {
        return new ServerStatus(
                smtpServer.isRunning(),
                smtpServer.getHostname(),
                smtpServer.getPort(),
                smtpServer.getMaxConnections(),
                smtpServer.getConnectionTimeout(),
                isStarting.get(),
                isStopping.get()
        );
    }
    
    /**
     * 应用配置到服务器
     */
    private void applyConfiguration() {
        SmtpServerProperties.Server serverConfig = properties.getServer();
        SmtpServerProperties.Mail mailConfig = properties.getMail();
        
        smtpServer.setHostname(serverConfig.getHostname());
        smtpServer.setPort(serverConfig.getPort());
        smtpServer.setMaxConnections(serverConfig.getMaxConnections());
        smtpServer.setConnectionTimeout(serverConfig.getConnectionTimeout());
        
        logger.debug("Applied configuration: hostname={}, port={}, maxConnections={}, timeout={}s",
                serverConfig.getHostname(),
                serverConfig.getPort(),
                serverConfig.getMaxConnections(),
                serverConfig.getConnectionTimeout());
    }
    
    /**
     * 服务器状态信息
     */
    public static class ServerStatus {
        private final boolean running;
        private final String hostname;
        private final int port;
        private final int maxConnections;
        private final int connectionTimeout;
        private final boolean starting;
        private final boolean stopping;
        
        public ServerStatus(boolean running, String hostname, int port, int maxConnections, 
                          int connectionTimeout, boolean starting, boolean stopping) {
            this.running = running;
            this.hostname = hostname;
            this.port = port;
            this.maxConnections = maxConnections;
            this.connectionTimeout = connectionTimeout;
            this.starting = starting;
            this.stopping = stopping;
        }
        
        // Getters
        public boolean isRunning() {
            return running;
        }
        
        public String getHostname() {
            return hostname;
        }
        
        public int getPort() {
            return port;
        }
        
        public int getMaxConnections() {
            return maxConnections;
        }
        
        public int getConnectionTimeout() {
            return connectionTimeout;
        }
        
        public boolean isStarting() {
            return starting;
        }
        
        public boolean isStopping() {
            return stopping;
        }
        
        @Override
        public String toString() {
            return String.format("ServerStatus{running=%s, hostname='%s', port=%d, maxConnections=%d, " +
                            "connectionTimeout=%d, starting=%s, stopping=%s}",
                    running, hostname, port, maxConnections, connectionTimeout, starting, stopping);
        }
    }
}
