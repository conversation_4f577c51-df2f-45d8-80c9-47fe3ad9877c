package com.netty.smtp.service;

import com.netty.smtp.model.EmailMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 本地邮件投递服务
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Service
public class LocalDeliveryService {
    
    private static final Logger logger = LoggerFactory.getLogger(LocalDeliveryService.class);
    private static final Logger deliveryLogger = LoggerFactory.getLogger("com.netty.smtp.service.delivery");
    
    private static final String MAILBOX_DIR = "mailboxes";
    private static final DateTimeFormatter TIMESTAMP_FORMAT = DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss_SSS");
    private static final DateTimeFormatter EMAIL_DATE_FORMAT = DateTimeFormatter.ofPattern("EEE, dd MMM yyyy HH:mm:ss");
    
    /**
     * 投递邮件到本地邮箱
     */
    public void deliverMessage(EmailMessage message, List<String> recipients) throws IOException {
        deliveryLogger.info("Delivering message {} to {} local recipients", 
                message.getMessageId(), recipients.size());
        
        // 确保邮箱目录存在
        ensureMailboxDirectory();
        
        for (String recipient : recipients) {
            try {
                deliverToRecipient(message, recipient);
                deliveryLogger.debug("Successfully delivered to local recipient: {}", recipient);
                
            } catch (Exception e) {
                deliveryLogger.error("Failed to deliver to local recipient {}: {}", recipient, e.getMessage());
                throw new IOException("Local delivery failed for " + recipient, e);
            }
        }
        
        deliveryLogger.info("Local delivery completed for message {}", message.getMessageId());
    }
    
    /**
     * 投递到单个收件人
     */
    private void deliverToRecipient(EmailMessage message, String recipient) throws IOException {
        // 创建收件人邮箱目录
        String mailboxName = sanitizeMailboxName(recipient);
        Path mailboxPath = Paths.get(MAILBOX_DIR, mailboxName);
        Files.createDirectories(mailboxPath);
        
        // 生成邮件文件名
        String timestamp = LocalDateTime.now().format(TIMESTAMP_FORMAT);
        String fileName = String.format("%s_%s.eml", timestamp, message.getMessageId());
        Path messagePath = mailboxPath.resolve(fileName);
        
        // 构建完整的邮件内容
        String fullMessage = buildFullMessage(message, recipient);
        
        // 写入邮件文件
        Files.write(messagePath, fullMessage.getBytes(), 
                StandardOpenOption.CREATE, StandardOpenOption.WRITE);
        
        deliveryLogger.debug("Message saved to: {}", messagePath);
    }
    
    /**
     * 构建完整的邮件内容
     */
    private String buildFullMessage(EmailMessage message, String recipient) {
        StringBuilder sb = new StringBuilder();
        
        // 添加投递头部信息
        sb.append("Delivered-To: ").append(recipient).append("\r\n");
        sb.append("Return-Path: <").append(message.getFrom()).append(">\r\n");
        sb.append("Received: from localhost by netty-smtp-server\r\n");
        sb.append("\twith SMTP id ").append(message.getMessageId()).append(";\r\n");
        sb.append("\t").append(LocalDateTime.now().format(EMAIL_DATE_FORMAT)).append("\r\n");
        
        // 添加原始邮件内容
        String rawData = message.getRawData();
        if (rawData != null && !rawData.trim().isEmpty()) {
            // 检查是否已有头部分隔符
            if (!rawData.startsWith("From:") && !rawData.startsWith("To:") && !rawData.startsWith("Subject:")) {
                // 如果没有标准头部，添加基本头部
                sb.append("From: ").append(message.getFrom()).append("\r\n");
                sb.append("To: ").append(recipient).append("\r\n");
                if (message.getSubject() != null) {
                    sb.append("Subject: ").append(message.getSubject()).append("\r\n");
                }
                sb.append("Date: ").append(LocalDateTime.now().format(EMAIL_DATE_FORMAT)).append("\r\n");
                sb.append("Message-ID: <").append(message.getMessageId()).append("@localhost>\r\n");
                sb.append("\r\n");
            }
            
            sb.append(rawData);
        } else {
            // 创建基本邮件结构
            sb.append("From: ").append(message.getFrom()).append("\r\n");
            sb.append("To: ").append(recipient).append("\r\n");
            sb.append("Subject: ").append(message.getSubject() != null ? message.getSubject() : "(No Subject)").append("\r\n");
            sb.append("Date: ").append(LocalDateTime.now().format(EMAIL_DATE_FORMAT)).append("\r\n");
            sb.append("Message-ID: <").append(message.getMessageId()).append("@localhost>\r\n");
            sb.append("\r\n");
            sb.append(message.getBody() != null ? message.getBody() : "");
        }
        
        return sb.toString();
    }
    
    /**
     * 清理邮箱名称，移除不安全字符
     */
    private String sanitizeMailboxName(String email) {
        return email.replaceAll("[^a-zA-Z0-9@._-]", "_");
    }
    
    /**
     * 确保邮箱目录存在
     */
    private void ensureMailboxDirectory() throws IOException {
        Path mailboxDir = Paths.get(MAILBOX_DIR);
        if (!Files.exists(mailboxDir)) {
            Files.createDirectories(mailboxDir);
            logger.info("Created mailbox directory: {}", mailboxDir.toAbsolutePath());
        }
    }
    
    /**
     * 获取收件人的邮箱路径
     */
    public Path getMailboxPath(String recipient) {
        String mailboxName = sanitizeMailboxName(recipient);
        return Paths.get(MAILBOX_DIR, mailboxName);
    }
    
    /**
     * 列出收件人的所有邮件
     */
    public List<Path> listMessages(String recipient) throws IOException {
        Path mailboxPath = getMailboxPath(recipient);
        
        if (!Files.exists(mailboxPath)) {
            return Collections.emptyList();
        }

        try (Stream<Path> stream = Files.list(mailboxPath)) {
            return stream
                    .filter(path -> path.toString().endsWith(".eml"))
                    .sorted()
                    .collect(Collectors.toList());
        }
    }
    
    /**
     * 读取邮件内容
     */
    public String readMessage(Path messagePath) throws IOException {
        return new String(Files.readAllBytes(messagePath), "UTF-8");
    }
    
    /**
     * 删除邮件
     */
    public boolean deleteMessage(Path messagePath) {
        try {
            return Files.deleteIfExists(messagePath);
        } catch (IOException e) {
            logger.error("Failed to delete message: {}", messagePath, e);
            return false;
        }
    }
    
    /**
     * 获取邮箱统计信息
     */
    public MailboxStats getMailboxStats(String recipient) {
        try {
            Path mailboxPath = getMailboxPath(recipient);
            
            if (!Files.exists(mailboxPath)) {
                return new MailboxStats(0, 0);
            }
            
            List<Path> messages = listMessages(recipient);
            long totalSize = messages.stream()
                    .mapToLong(path -> {
                        try {
                            return Files.size(path);
                        } catch (IOException e) {
                            return 0;
                        }
                    })
                    .sum();
            
            return new MailboxStats(messages.size(), totalSize);
            
        } catch (IOException e) {
            logger.error("Failed to get mailbox stats for {}", recipient, e);
            return new MailboxStats(0, 0);
        }
    }
    
    /**
     * 邮箱统计信息
     */
    public static class MailboxStats {
        private final int messageCount;
        private final long totalSize;
        
        public MailboxStats(int messageCount, long totalSize) {
            this.messageCount = messageCount;
            this.totalSize = totalSize;
        }
        
        public int getMessageCount() {
            return messageCount;
        }
        
        public long getTotalSize() {
            return totalSize;
        }
        
        @Override
        public String toString() {
            return String.format("MailboxStats{messages=%d, size=%d bytes}", messageCount, totalSize);
        }
    }
}
