package com.netty.smtp.service;

import com.netty.smtp.config.SmtpServerProperties;
import com.netty.smtp.model.EmailMessage;
import com.netty.smtp.util.DnsResolver;
import jakarta.mail.*;
import jakarta.mail.internet.InternetAddress;
import jakarta.mail.internet.MimeMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Properties;

/**
 * 邮件转发服务 - 负责将邮件转发到外部SMTP服务器
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Service
public class EmailRelayService {
    
    private static final Logger logger = LoggerFactory.getLogger(EmailRelayService.class);
    private static final Logger deliveryLogger = LoggerFactory.getLogger("com.netty.smtp.service.delivery");
    
    @Autowired
    private SmtpServerProperties properties;
    
    @Autowired
    private DnsResolver dnsResolver;
    
    /**
     * 转发邮件到指定域名
     */
    public void relayMessage(EmailMessage message, String domain, List<String> recipients) throws MessagingException {
        deliveryLogger.info("Relaying message {} to domain {} for {} recipients", 
                message.getMessageId(), domain, recipients.size());
        
        // 获取目标SMTP服务器信息
        SmtpServerInfo serverInfo = resolveSmtpServer(domain);
        
        deliveryLogger.debug("Using SMTP server: {}:{} for domain {}", 
                serverInfo.getHost(), serverInfo.getPort(), domain);
        
        // 创建邮件会话
        Session session = createMailSession(serverInfo);
        
        try {
            // 创建MIME消息
            MimeMessage mimeMessage = createMimeMessage(session, message, recipients);
            
            // 发送邮件
            Transport.send(mimeMessage);
            
            deliveryLogger.info("Successfully relayed message {} to domain {} for {} recipients", 
                    message.getMessageId(), domain, recipients.size());
            
        } catch (MessagingException e) {
            deliveryLogger.error("Failed to relay message {} to domain {}: {}", 
                    message.getMessageId(), domain, e.getMessage());
            throw e;
        }
    }
    
    /**
     * 解析SMTP服务器信息
     */
    private SmtpServerInfo resolveSmtpServer(String domain) {
        // 首先检查配置的路由表
        String routingConfig = properties.getDomains().getRouting().get(domain);
        if (routingConfig != null) {
            String[] parts = routingConfig.split(":");
            String host = parts[0];
            int port = parts.length > 1 ? Integer.parseInt(parts[1]) : 25;
            return new SmtpServerInfo(host, port, false);
        }
        
        // 尝试DNS MX记录查询
        try {
            String mxHost = dnsResolver.resolveMxRecord(domain);
            if (mxHost != null) {
                // 根据常见邮件服务商判断是否需要TLS
                boolean requiresTls = isKnownTlsProvider(mxHost);
                int port = requiresTls ? 587 : 25;
                return new SmtpServerInfo(mxHost, port, requiresTls);
            }
        } catch (Exception e) {
            deliveryLogger.warn("Failed to resolve MX record for domain {}: {}", domain, e.getMessage());
        }
        
        // 使用默认配置
        SmtpServerProperties.Relay relayConfig = properties.getRelay();
        return new SmtpServerInfo(
                relayConfig.getDefaultHost(), 
                relayConfig.getDefaultPort(), 
                true
        );
    }
    
    /**
     * 判断是否为已知的需要TLS的邮件服务商
     */
    private boolean isKnownTlsProvider(String host) {
        String lowerHost = host.toLowerCase();
        return lowerHost.contains("gmail") || 
               lowerHost.contains("outlook") || 
               lowerHost.contains("office365") ||
               lowerHost.contains("yahoo") ||
               lowerHost.contains("qq.com") ||
               lowerHost.contains("163.com");
    }
    
    /**
     * 创建邮件会话
     */
    private Session createMailSession(SmtpServerInfo serverInfo) {
        Properties props = new Properties();
        props.put("mail.smtp.host", serverInfo.getHost());
        props.put("mail.smtp.port", String.valueOf(serverInfo.getPort()));
        props.put("mail.smtp.timeout", "30000");
        props.put("mail.smtp.connectiontimeout", "30000");
        
        SmtpServerProperties.Relay relayConfig = properties.getRelay();
        
        if (serverInfo.isRequiresTls()) {
            props.put("mail.smtp.starttls.enable", "true");
            props.put("mail.smtp.starttls.required", "true");
            props.put("mail.smtp.ssl.protocols", "TLSv1.2");
        }
        
        // 如果配置了认证信息
        if (relayConfig.getAuth().getUsername() != null && 
            !relayConfig.getAuth().getUsername().isEmpty()) {
            props.put("mail.smtp.auth", "true");
            
            return Session.getInstance(props, new Authenticator() {
                @Override
                protected PasswordAuthentication getPasswordAuthentication() {
                    return new PasswordAuthentication(
                            relayConfig.getAuth().getUsername(),
                            relayConfig.getAuth().getPassword()
                    );
                }
            });
        } else {
            return Session.getInstance(props);
        }
    }
    
    /**
     * 创建MIME消息
     */
    private MimeMessage createMimeMessage(Session session, EmailMessage message, List<String> recipients) 
            throws MessagingException {
        
        MimeMessage mimeMessage = new MimeMessage(session);
        
        // 设置发件人
        if (message.getFrom() != null && !message.getFrom().isEmpty()) {
            mimeMessage.setFrom(new InternetAddress(message.getFrom()));
        }
        
        // 设置收件人
        for (String recipient : recipients) {
            mimeMessage.addRecipient(Message.RecipientType.TO, new InternetAddress(recipient));
        }
        
        // 如果有原始邮件数据，尝试解析
        if (message.getRawData() != null && !message.getRawData().trim().isEmpty()) {
            parseRawMessage(mimeMessage, message.getRawData());
        } else {
            // 使用基本信息创建邮件
            if (message.getSubject() != null) {
                mimeMessage.setSubject(message.getSubject());
            }
            if (message.getBody() != null) {
                mimeMessage.setText(message.getBody());
            }
        }
        
        // 添加转发头部
        mimeMessage.addHeader("X-Relayed-By", "netty-smtp-server");
        mimeMessage.addHeader("X-Original-Message-ID", message.getMessageId());
        
        return mimeMessage;
    }
    
    /**
     * 解析原始邮件数据
     */
    private void parseRawMessage(MimeMessage mimeMessage, String rawData) throws MessagingException {
        String[] lines = rawData.split("\r?\n");
        boolean inHeaders = true;
        StringBuilder bodyBuilder = new StringBuilder();
        
        for (String line : lines) {
            if (inHeaders) {
                if (line.trim().isEmpty()) {
                    inHeaders = false;
                    continue;
                }
                
                // 解析头部
                if (line.toLowerCase().startsWith("subject:")) {
                    mimeMessage.setSubject(line.substring(8).trim());
                } else if (line.toLowerCase().startsWith("content-type:")) {
                    // 处理Content-Type头部
                    String contentType = line.substring(13).trim();
                    if (contentType.toLowerCase().contains("text/html")) {
                        // 后续设置为HTML内容
                    }
                }
                // 其他头部可以根据需要添加
            } else {
                bodyBuilder.append(line).append("\n");
            }
        }
        
        String body = bodyBuilder.toString().trim();
        if (!body.isEmpty()) {
            mimeMessage.setText(body);
        }
    }
    
    /**
     * SMTP服务器信息
     */
    private static class SmtpServerInfo {
        private final String host;
        private final int port;
        private final boolean requiresTls;
        
        public SmtpServerInfo(String host, int port, boolean requiresTls) {
            this.host = host;
            this.port = port;
            this.requiresTls = requiresTls;
        }
        
        public String getHost() {
            return host;
        }
        
        public int getPort() {
            return port;
        }
        
        public boolean isRequiresTls() {
            return requiresTls;
        }
        
        @Override
        public String toString() {
            return String.format("SmtpServerInfo{host='%s', port=%d, requiresTls=%s}", 
                    host, port, requiresTls);
        }
    }
}
