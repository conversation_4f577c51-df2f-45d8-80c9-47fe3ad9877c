package com.netty.smtp.controller;

import com.netty.smtp.service.SmtpServerService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * SMTP服务器管理控制器 - 简化版本
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@RestController
@RequestMapping("/api/smtp")
public class SmtpServerController {

    private static final Logger logger = LoggerFactory.getLogger(SmtpServerController.class);
    
    @Autowired
    private SmtpServerService smtpServerService;
    
    /**
     * 获取服务器状态
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getServerStatus() {
        try {
            SmtpServerService.ServerStatus status = smtpServerService.getServerStatus();
            
            Map<String, Object> response = new HashMap<>();
            response.put("running", status.isRunning());
            response.put("hostname", status.getHostname());
            response.put("port", status.getPort());
            response.put("maxConnections", status.getMaxConnections());
            response.put("connectionTimeout", status.getConnectionTimeout());
            response.put("starting", status.isStarting());
            response.put("stopping", status.isStopping());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("Failed to get server status", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", "Failed to get server status: " + e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }
    
    /**
     * 启动服务器
     */
    @PostMapping("/start")
    public ResponseEntity<Map<String, Object>> startServer() {
        try {
            smtpServerService.startServer().get();
            
            Map<String, Object> response = new HashMap<>();
            response.put("message", "SMTP server started successfully");
            response.put("status", smtpServerService.getServerStatus());
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("Failed to start server", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", "Failed to start server: " + e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }
    
    /**
     * 停止服务器
     */
    @PostMapping("/stop")
    public ResponseEntity<Map<String, Object>> stopServer() {
        try {
            smtpServerService.stopServer().get();
            
            Map<String, Object> response = new HashMap<>();
            response.put("message", "SMTP server stopped successfully");
            response.put("status", smtpServerService.getServerStatus());
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("Failed to stop server", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", "Failed to stop server: " + e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }
    
    /**
     * 重启服务器
     */
    @PostMapping("/restart")
    public ResponseEntity<Map<String, Object>> restartServer() {
        try {
            smtpServerService.restartServer().get();
            
            Map<String, Object> response = new HashMap<>();
            response.put("message", "SMTP server restarted successfully");
            response.put("status", smtpServerService.getServerStatus());
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("Failed to restart server", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", "Failed to restart server: " + e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }
}
