package com.netty.smtp;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;

/**
 * Netty SMTP Server 主启动类
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@SpringBootApplication
@EnableConfigurationProperties
public class NettySmtpServerApplication {

    public static void main(String[] args) {
        SpringApplication.run(NettySmtpServerApplication.class, args);
    }
}
