package com.netty.smtp.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.xbill.DNS.*;

import java.util.ArrayList;
import java.util.List;

/**
 * DNS解析工具类
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Component
public class DnsResolver {
    
    private static final Logger logger = LoggerFactory.getLogger(DnsResolver.class);
    
    /**
     * 解析域名的MX记录
     */
    public String resolveMxRecord(String domain) {
        try {
            logger.debug("Resolving MX record for domain: {}", domain);
            
            Lookup lookup = new Lookup(domain, Type.MX);
            Record[] records = lookup.run();
            
            if (records == null || records.length == 0) {
                logger.warn("No MX records found for domain: {}", domain);
                return null;
            }
            
            // 按优先级排序MX记录
            List<MXRecord> mxRecords = new ArrayList<>();
            for (Record record : records) {
                if (record instanceof MXRecord) {
                    mxRecords.add((MXRecord) record);
                }
            }
            
            if (mxRecords.isEmpty()) {
                logger.warn("No valid MX records found for domain: {}", domain);
                return null;
            }
            
            // 选择优先级最高的MX记录
            MXRecord bestMx = mxRecords.stream()
                    .min((mx1, mx2) -> Integer.compare(mx1.getPriority(), mx2.getPriority()))
                    .orElse(null);
            
            if (bestMx != null) {
                String mxHost = bestMx.getTarget().toString();
                if (mxHost.endsWith(".")) {
                    mxHost = mxHost.substring(0, mxHost.length() - 1);
                }
                
                logger.debug("Resolved MX record for {}: {} (priority: {})", 
                        domain, mxHost, bestMx.getPriority());
                
                return mxHost;
            }
            
        } catch (Exception e) {
            logger.error("Failed to resolve MX record for domain {}: {}", domain, e.getMessage());
        }
        
        return null;
    }
    
    /**
     * 解析域名的A记录
     */
    public List<String> resolveARecord(String hostname) {
        List<String> addresses = new ArrayList<>();
        
        try {
            logger.debug("Resolving A record for hostname: {}", hostname);
            
            Lookup lookup = new Lookup(hostname, Type.A);
            Record[] records = lookup.run();
            
            if (records != null) {
                for (Record record : records) {
                    if (record instanceof ARecord) {
                        ARecord aRecord = (ARecord) record;
                        addresses.add(aRecord.getAddress().getHostAddress());
                    }
                }
            }
            
            logger.debug("Resolved {} A records for hostname {}: {}", 
                    addresses.size(), hostname, addresses);
            
        } catch (Exception e) {
            logger.error("Failed to resolve A record for hostname {}: {}", hostname, e.getMessage());
        }
        
        return addresses;
    }
    
    /**
     * 解析域名的AAAA记录（IPv6）
     */
    public List<String> resolveAAAARecord(String hostname) {
        List<String> addresses = new ArrayList<>();
        
        try {
            logger.debug("Resolving AAAA record for hostname: {}", hostname);
            
            Lookup lookup = new Lookup(hostname, Type.AAAA);
            Record[] records = lookup.run();
            
            if (records != null) {
                for (Record record : records) {
                    if (record instanceof AAAARecord) {
                        AAAARecord aaaaRecord = (AAAARecord) record;
                        addresses.add(aaaaRecord.getAddress().getHostAddress());
                    }
                }
            }
            
            logger.debug("Resolved {} AAAA records for hostname {}: {}", 
                    addresses.size(), hostname, addresses);
            
        } catch (Exception e) {
            logger.error("Failed to resolve AAAA record for hostname {}: {}", hostname, e.getMessage());
        }
        
        return addresses;
    }
    
    /**
     * 解析域名的TXT记录
     */
    public List<String> resolveTxtRecord(String domain) {
        List<String> txtRecords = new ArrayList<>();
        
        try {
            logger.debug("Resolving TXT record for domain: {}", domain);
            
            Lookup lookup = new Lookup(domain, Type.TXT);
            Record[] records = lookup.run();
            
            if (records != null) {
                for (Record record : records) {
                    if (record instanceof TXTRecord) {
                        TXTRecord txtRecord = (TXTRecord) record;
                        txtRecords.addAll(txtRecord.getStrings());
                    }
                }
            }
            
            logger.debug("Resolved {} TXT records for domain {}", txtRecords.size(), domain);
            
        } catch (Exception e) {
            logger.error("Failed to resolve TXT record for domain {}: {}", domain, e.getMessage());
        }
        
        return txtRecords;
    }
    
    /**
     * 检查域名是否存在
     */
    public boolean domainExists(String domain) {
        try {
            // 尝试解析MX记录
            String mxRecord = resolveMxRecord(domain);
            if (mxRecord != null) {
                return true;
            }
            
            // 尝试解析A记录
            List<String> aRecords = resolveARecord(domain);
            if (!aRecords.isEmpty()) {
                return true;
            }
            
            // 尝试解析AAAA记录
            List<String> aaaaRecords = resolveAAAARecord(domain);
            return !aaaaRecords.isEmpty();
            
        } catch (Exception e) {
            logger.debug("Domain {} does not exist or is not resolvable: {}", domain, e.getMessage());
            return false;
        }
    }
    
    /**
     * 获取域名的所有MX记录（按优先级排序）
     */
    public List<MxRecord> getAllMxRecords(String domain) {
        List<MxRecord> mxRecords = new ArrayList<>();
        
        try {
            Lookup lookup = new Lookup(domain, Type.MX);
            Record[] records = lookup.run();
            
            if (records != null) {
                for (Record record : records) {
                    if (record instanceof MXRecord) {
                        MXRecord mxRecord = (MXRecord) record;
                        String host = mxRecord.getTarget().toString();
                        if (host.endsWith(".")) {
                            host = host.substring(0, host.length() - 1);
                        }
                        mxRecords.add(new MxRecord(host, mxRecord.getPriority()));
                    }
                }
            }
            
            // 按优先级排序
            mxRecords.sort((mx1, mx2) -> Integer.compare(mx1.getPriority(), mx2.getPriority()));
            
        } catch (Exception e) {
            logger.error("Failed to resolve all MX records for domain {}: {}", domain, e.getMessage());
        }
        
        return mxRecords;
    }
    
    /**
     * MX记录信息
     */
    public static class MxRecord {
        private final String host;
        private final int priority;
        
        public MxRecord(String host, int priority) {
            this.host = host;
            this.priority = priority;
        }
        
        public String getHost() {
            return host;
        }
        
        public int getPriority() {
            return priority;
        }
        
        @Override
        public String toString() {
            return String.format("MxRecord{host='%s', priority=%d}", host, priority);
        }
    }
}
