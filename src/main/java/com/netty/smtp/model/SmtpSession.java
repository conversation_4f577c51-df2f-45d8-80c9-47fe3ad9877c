package com.netty.smtp.model;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * SMTP会话状态管理
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class SmtpSession {
    
    /**
     * SMTP会话状态枚举
     */
    public enum State {
        INITIAL,        // 初始状态
        GREETED,        // 已问候(HELO/EHLO)
        MAIL_STARTED,   // 已开始邮件(MAIL FROM)
        RCPT_ADDED,     // 已添加收件人(RCPT TO)
        DATA_STARTED,   // 已开始数据传输(DATA)
        DATA_RECEIVING, // 正在接收数据
        COMPLETED,      // 邮件传输完成
        QUIT            // 会话结束
    }
    
    private final String sessionId;
    private final String clientAddress;
    private final LocalDateTime createdTime;
    
    private State state;
    private String clientHostname;
    private String mailFrom;
    private List<String> recipients;
    private StringBuilder messageData;
    private boolean isExtendedSmtp;
    private long messageSize;
    private LocalDateTime lastActivityTime;
    
    public SmtpSession(String clientAddress) {
        this.sessionId = UUID.randomUUID().toString();
        this.clientAddress = clientAddress;
        this.createdTime = LocalDateTime.now();
        this.lastActivityTime = LocalDateTime.now();
        this.state = State.INITIAL;
        this.recipients = new ArrayList<>();
        this.messageData = new StringBuilder();
        this.isExtendedSmtp = false;
        this.messageSize = 0;
    }
    
    /**
     * 重置会话状态（用于RSET命令）
     */
    public void reset() {
        this.state = State.GREETED;
        this.mailFrom = null;
        this.recipients.clear();
        this.messageData = new StringBuilder();
        this.messageSize = 0;
        updateActivity();
    }
    
    /**
     * 更新最后活动时间
     */
    public void updateActivity() {
        this.lastActivityTime = LocalDateTime.now();
    }
    
    /**
     * 检查会话是否超时
     */
    public boolean isTimeout(int timeoutSeconds) {
        return lastActivityTime.plusSeconds(timeoutSeconds).isBefore(LocalDateTime.now());
    }
    
    /**
     * 添加收件人
     */
    public void addRecipient(String recipient) {
        this.recipients.add(recipient);
        updateActivity();
    }
    
    /**
     * 添加消息数据
     */
    public void appendMessageData(String data) {
        this.messageData.append(data);
        this.messageSize += data.length();
        updateActivity();
    }
    
    /**
     * 获取完整的消息数据
     */
    public String getCompleteMessage() {
        return messageData.toString();
    }
    
    /**
     * 清空消息数据
     */
    public void clearMessageData() {
        this.messageData = new StringBuilder();
        this.messageSize = 0;
    }
    
    // Getters and Setters
    public String getSessionId() {
        return sessionId;
    }
    
    public String getClientAddress() {
        return clientAddress;
    }
    
    public LocalDateTime getCreatedTime() {
        return createdTime;
    }
    
    public State getState() {
        return state;
    }
    
    public void setState(State state) {
        this.state = state;
        updateActivity();
    }
    
    public String getClientHostname() {
        return clientHostname;
    }
    
    public void setClientHostname(String clientHostname) {
        this.clientHostname = clientHostname;
        updateActivity();
    }
    
    public String getMailFrom() {
        return mailFrom;
    }
    
    public void setMailFrom(String mailFrom) {
        this.mailFrom = mailFrom;
        updateActivity();
    }
    
    public List<String> getRecipients() {
        return new ArrayList<>(recipients);
    }
    
    public boolean isExtendedSmtp() {
        return isExtendedSmtp;
    }
    
    public void setExtendedSmtp(boolean extendedSmtp) {
        isExtendedSmtp = extendedSmtp;
        updateActivity();
    }
    
    public long getMessageSize() {
        return messageSize;
    }
    
    public LocalDateTime getLastActivityTime() {
        return lastActivityTime;
    }
    
    @Override
    public String toString() {
        return "SmtpSession{" +
                "sessionId='" + sessionId + '\'' +
                ", clientAddress='" + clientAddress + '\'' +
                ", state=" + state +
                ", clientHostname='" + clientHostname + '\'' +
                ", mailFrom='" + mailFrom + '\'' +
                ", recipients=" + recipients.size() +
                ", messageSize=" + messageSize +
                ", isExtendedSmtp=" + isExtendedSmtp +
                '}';
    }
}
