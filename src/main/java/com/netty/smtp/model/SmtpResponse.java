package com.netty.smtp.model;

/**
 * SMTP响应类
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class SmtpResponse {
    
    private final int code;
    private final String message;
    private final boolean isMultiLine;
    
    public SmtpResponse(int code, String message) {
        this(code, message, false);
    }
    
    public SmtpResponse(int code, String message, boolean isMultiLine) {
        this.code = code;
        this.message = message;
        this.isMultiLine = isMultiLine;
    }
    
    public int getCode() {
        return code;
    }
    
    public String getMessage() {
        return message;
    }
    
    public boolean isMultiLine() {
        return isMultiLine;
    }
    
    /**
     * 判断是否为成功响应
     */
    public boolean isSuccess() {
        return (code >= 200 && code < 300) || code == 354;
    }
    
    /**
     * 判断是否为临时失败
     */
    public boolean isTemporaryFailure() {
        return code >= 400 && code < 500;
    }
    
    /**
     * 判断是否为永久失败
     */
    public boolean isPermanentFailure() {
        return code >= 500 && code < 600;
    }
    
    /**
     * 转换为SMTP协议格式的字符串
     */
    public String toSmtpString() {
        if (isMultiLine) {
            StringBuilder sb = new StringBuilder();
            String[] lines = message.split("\n");
            for (int i = 0; i < lines.length; i++) {
                sb.append(code);
                if (i < lines.length - 1) {
                    sb.append("-");
                } else {
                    sb.append(" ");
                }
                sb.append(lines[i]);
                if (i < lines.length - 1) {
                    sb.append("\r\n");
                }
            }
            return sb.toString();
        } else {
            return code + " " + message;
        }
    }
    
    @Override
    public String toString() {
        return "SmtpResponse{" +
                "code=" + code +
                ", message='" + message + '\'' +
                ", isMultiLine=" + isMultiLine +
                '}';
    }
    
    // 常用的SMTP响应
    public static class Responses {
        public static final SmtpResponse SERVICE_READY = new SmtpResponse(220, "Service ready");
        public static final SmtpResponse CLOSING_CHANNEL = new SmtpResponse(221, "Service closing transmission channel");
        public static final SmtpResponse OK = new SmtpResponse(250, "OK");
        public static final SmtpResponse START_MAIL_INPUT = new SmtpResponse(354, "Start mail input; end with <CRLF>.<CRLF>");
        
        public static final SmtpResponse SYNTAX_ERROR = new SmtpResponse(500, "Syntax error, command unrecognized");
        public static final SmtpResponse PARAMETER_ERROR = new SmtpResponse(501, "Syntax error in parameters or arguments");
        public static final SmtpResponse COMMAND_NOT_IMPLEMENTED = new SmtpResponse(502, "Command not implemented");
        public static final SmtpResponse BAD_SEQUENCE = new SmtpResponse(503, "Bad sequence of commands");
        public static final SmtpResponse PARAMETER_NOT_IMPLEMENTED = new SmtpResponse(504, "Command parameter not implemented");
        
        public static final SmtpResponse MAILBOX_UNAVAILABLE = new SmtpResponse(550, "Requested action not taken: mailbox unavailable");
        public static final SmtpResponse USER_NOT_LOCAL = new SmtpResponse(551, "User not local; please try forwarding");
        public static final SmtpResponse INSUFFICIENT_STORAGE = new SmtpResponse(552, "Requested mail action aborted: exceeded storage allocation");
        public static final SmtpResponse MAILBOX_NAME_NOT_ALLOWED = new SmtpResponse(553, "Requested action not taken: mailbox name not allowed");
        public static final SmtpResponse TRANSACTION_FAILED = new SmtpResponse(554, "Transaction failed");
        
        public static SmtpResponse serviceReady(String hostname) {
            return new SmtpResponse(220, hostname + " ESMTP Service ready");
        }
        
        public static SmtpResponse hello(String hostname) {
            return new SmtpResponse(250, hostname + " Hello", true);
        }
        
        public static SmtpResponse mailOk(String from) {
            return new SmtpResponse(250, "OK, mail from " + from);
        }
        
        public static SmtpResponse rcptOk(String to) {
            return new SmtpResponse(250, "OK, recipient " + to);
        }
        
        public static SmtpResponse invalidAddress(String address) {
            return new SmtpResponse(553, "Invalid address: " + address);
        }
        
        public static SmtpResponse tooManyRecipients(int max) {
            return new SmtpResponse(452, "Too many recipients (max " + max + ")");
        }
        
        public static SmtpResponse messageTooLarge(long maxSize) {
            return new SmtpResponse(552, "Message size exceeds maximum allowed (" + maxSize + " bytes)");
        }
    }
}
