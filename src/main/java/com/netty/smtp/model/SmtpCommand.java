package com.netty.smtp.model;

/**
 * SMTP命令枚举
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public enum SmtpCommand {
    
    // 连接和认证命令
    HELO("HELO"),
    EHLO("EHLO"),
    AUTH("AUTH"),
    STARTTLS("STARTTLS"),
    
    // 邮件传输命令
    MAIL("MAIL"),
    RCPT("RCPT"),
    DATA("DATA"),
    RSET("RSET"),
    
    // 连接管理命令
    NOOP("NOOP"),
    QUIT("QUIT"),
    
    // 扩展命令
    VRFY("VRFY"),
    EXPN("EXPN"),
    HELP("HELP"),
    
    // 未知命令
    UNKNOWN("UNKNOWN");
    
    private final String command;
    
    SmtpCommand(String command) {
        this.command = command;
    }
    
    public String getCommand() {
        return command;
    }
    
    /**
     * 根据字符串解析SMTP命令
     */
    public static SmtpCommand fromString(String commandStr) {
        if (commandStr == null || commandStr.trim().isEmpty()) {
            return UNKNOWN;
        }
        
        String upperCommand = commandStr.trim().toUpperCase();
        
        for (SmtpCommand cmd : values()) {
            if (cmd.command.equals(upperCommand)) {
                return cmd;
            }
        }
        
        return UNKNOWN;
    }
    
    @Override
    public String toString() {
        return command;
    }
}
