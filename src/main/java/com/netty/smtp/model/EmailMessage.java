package com.netty.smtp.model;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * 邮件消息模型
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class EmailMessage {
    
    private final String messageId;
    private final String sessionId;
    private final LocalDateTime receivedTime;
    
    private String from;
    private List<String> recipients;
    private String rawData;
    private String subject;
    private String body;
    private boolean isProcessed;
    private LocalDateTime processedTime;
    private String errorMessage;
    
    public EmailMessage(String sessionId) {
        this.messageId = UUID.randomUUID().toString();
        this.sessionId = sessionId;
        this.receivedTime = LocalDateTime.now();
        this.recipients = new ArrayList<>();
        this.isProcessed = false;
    }
    
    public EmailMessage(String sessionId, String from, List<String> recipients, String rawData) {
        this(sessionId);
        this.from = from;
        this.recipients = new ArrayList<>(recipients);
        this.rawData = rawData;
        parseMessage();
    }
    
    /**
     * 解析邮件内容
     */
    private void parseMessage() {
        if (rawData == null || rawData.trim().isEmpty()) {
            return;
        }
        
        String[] lines = rawData.split("\r?\n");
        boolean inHeaders = true;
        StringBuilder bodyBuilder = new StringBuilder();
        
        for (String line : lines) {
            if (inHeaders) {
                if (line.trim().isEmpty()) {
                    inHeaders = false;
                    continue;
                }
                
                // 解析Subject
                if (line.toLowerCase().startsWith("subject:")) {
                    this.subject = line.substring(8).trim();
                }
            } else {
                bodyBuilder.append(line).append("\n");
            }
        }
        
        this.body = bodyBuilder.toString().trim();
    }
    
    /**
     * 标记为已处理
     */
    public void markAsProcessed() {
        this.isProcessed = true;
        this.processedTime = LocalDateTime.now();
    }
    
    /**
     * 标记处理失败
     */
    public void markAsFailed(String errorMessage) {
        this.isProcessed = true;
        this.processedTime = LocalDateTime.now();
        this.errorMessage = errorMessage;
    }
    
    /**
     * 添加收件人
     */
    public void addRecipient(String recipient) {
        if (!this.recipients.contains(recipient)) {
            this.recipients.add(recipient);
        }
    }
    
    /**
     * 获取邮件大小（字节）
     */
    public long getSize() {
        return rawData != null ? rawData.getBytes().length : 0;
    }
    
    /**
     * 检查是否为本地域名
     */
    public boolean isLocalDomain(String domain, List<String> localDomains) {
        if (domain == null || localDomains == null) {
            return false;
        }
        
        return localDomains.stream()
                .anyMatch(localDomain -> domain.equalsIgnoreCase(localDomain));
    }
    
    /**
     * 从邮件地址中提取域名
     */
    public static String extractDomain(String email) {
        if (email == null || !email.contains("@")) {
            return null;
        }
        
        int atIndex = email.lastIndexOf("@");
        if (atIndex < email.length() - 1) {
            return email.substring(atIndex + 1).toLowerCase();
        }
        
        return null;
    }
    
    // Getters and Setters
    public String getMessageId() {
        return messageId;
    }
    
    public String getSessionId() {
        return sessionId;
    }
    
    public LocalDateTime getReceivedTime() {
        return receivedTime;
    }
    
    public String getFrom() {
        return from;
    }
    
    public void setFrom(String from) {
        this.from = from;
    }
    
    public List<String> getRecipients() {
        return new ArrayList<>(recipients);
    }
    
    public void setRecipients(List<String> recipients) {
        this.recipients = new ArrayList<>(recipients);
    }
    
    public String getRawData() {
        return rawData;
    }
    
    public void setRawData(String rawData) {
        this.rawData = rawData;
        parseMessage();
    }
    
    public String getSubject() {
        return subject;
    }
    
    public void setSubject(String subject) {
        this.subject = subject;
    }
    
    public String getBody() {
        return body;
    }
    
    public void setBody(String body) {
        this.body = body;
    }
    
    public boolean isProcessed() {
        return isProcessed;
    }
    
    public LocalDateTime getProcessedTime() {
        return processedTime;
    }
    
    public String getErrorMessage() {
        return errorMessage;
    }
    
    @Override
    public String toString() {
        return "EmailMessage{" +
                "messageId='" + messageId + '\'' +
                ", sessionId='" + sessionId + '\'' +
                ", from='" + from + '\'' +
                ", recipients=" + recipients.size() +
                ", subject='" + subject + '\'' +
                ", size=" + getSize() +
                ", isProcessed=" + isProcessed +
                '}';
    }
}
