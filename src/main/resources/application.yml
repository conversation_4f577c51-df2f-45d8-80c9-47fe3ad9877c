server:
  port: 8080

# SMTP服务器配置
smtp:
  server:
    # SMTP服务器监听端口
    port: 2525
    # 服务器主机名
    hostname: localhost
    # 最大连接数
    max-connections: 100
    # 连接超时时间(秒)
    connection-timeout: 300
    # 是否启用SSL/TLS
    ssl-enabled: false
    # SSL端口
    ssl-port: 2465
    # 是否启用STARTTLS
    starttls-enabled: true
    
  # 邮件处理配置
  mail:
    # 最大邮件大小(MB)
    max-size: 25
    # 最大收件人数量
    max-recipients: 100
    # 邮件队列大小
    queue-size: 1000
    # 邮件处理线程数
    worker-threads: 10
    
  # 转发配置
  relay:
    # 是否启用邮件转发
    enabled: true
    # 默认转发服务器
    default-host: smtp.gmail.com
    default-port: 587
    # 转发认证
    auth:
      username: ${SMTP_USERNAME:}
      password: ${SMTP_PASSWORD:}
    # 重试配置
    retry:
      max-attempts: 3
      delay-seconds: 5
      
  # 本地域名配置
  domains:
    # 本地处理的域名列表
    local:
      - localhost
      - 127.0.0.1
      - local.test
    # 域名路由配置
    routing:
      gmail.com: smtp.gmail.com:587
      outlook.com: smtp-mail.outlook.com:587
      yahoo.com: smtp.mail.yahoo.com:587
      qq.com: smtp.qq.com:587
      163.com: smtp.163.com:25

# 日志配置
logging:
  level:
    com.netty.smtp: DEBUG
    io.netty: INFO
    root: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%logger{36}] - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%logger{36}] - %msg%n"
  file:
    name: logs/smtp-server.log
    max-size: 100MB
    max-history: 30

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
